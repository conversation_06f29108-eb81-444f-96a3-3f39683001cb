import { useQuery } from "@tanstack/react-query";
import { apiClient } from '@/src/lib/api/client';
import { OrganizationsAPIResponse, ErrorAPIResponse } from '@/src/lib/types/api';

// Re-export types for backward compatibility
export type { OrganizationResponse as Organization } from '@/src/lib/types/api';
export type { OrganizationsAPIResponse as OrganizationsResponse } from '@/src/lib/types/api';

export interface OrganizationsError {
  message: string;
  missingProviders?: string[];
  requiresVCSConnection?: boolean;
}

export function useOrganizations() {
  return useQuery<OrganizationsAPIResponse, ErrorAPIResponse>({
    queryKey: ['organizations'],
    queryFn: async () => {
      try {
        const response = await apiClient.get<OrganizationsAPIResponse>('/api/organizations');
        return response.data;
      } catch (error) {
        // Handle API client errors
        if (error && typeof error === 'object' && 'type' in error) {
          // This is a PlatyfendError from the API client
          throw error;
        }

        // Handle other errors
        throw {
          success: false,
          error: 'Failed to fetch organizations',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          timestamp: new Date().toISOString(),
        } as ErrorAPIResponse;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error && 'error' in error && error.error?.includes('Not authenticated')) {
        return false;
      }
      // Don't retry on client errors (4xx)
      if (error && 'type' in error && error.type === 'external_api_error' &&
          'statusCode' in error && typeof error.statusCode === 'number' &&
          error.statusCode >= 400 && error.statusCode < 500) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
}
