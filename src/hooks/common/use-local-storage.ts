/**
 * Standardized Local Storage Hook
 * Provides type-safe local storage with serialization, validation, and SSR support
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { z } from 'zod';

/**
 * Storage type
 */
export type StorageType = 'local' | 'session';

/**
 * Storage options
 */
export interface StorageOptions<T> {
  // Validation
  schema?: z.ZodSchema<T>;

  // Serialization
  serialize?: (value: T) => string;
  deserialize?: (value: string) => T;

  // Error handling
  onError?: (error: Error, key: string) => void;

  // SSR
  ssr?: boolean;

  // Sync across tabs
  syncAcrossTabs?: boolean;
}

/**
 * Storage hook return type
 */
export interface UseStorageReturn<T> {
  value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Default serialization functions
 */
const defaultSerialize = <T>(value: T): string => JSON.stringify(value);
const defaultDeserialize = <T>(value: string): T => JSON.parse(value);

/**
 * Check if we're in a browser environment
 */
const isBrowser = typeof window !== 'undefined';

/**
 * Get storage object based on type
 */
function getStorage(storageType: StorageType): Storage | null {
  if (!isBrowser) {
    return null;
  }

  switch (storageType) {
    case 'local':
      return window.localStorage;
    case 'session':
      return window.sessionStorage;
    default:
      return window.localStorage;
  }
}

/**
 * Safe storage access
 */
function getStorageValue<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T>,
  storageType: StorageType = 'local'
): T {
  if (!isBrowser) {
    return defaultValue;
  }

  try {
    const storage = getStorage(storageType);
    if (!storage) {
      return defaultValue;
    }

    const item = storage.getItem(key);

    if (item === null) {
      return defaultValue;
    }

    const deserialize = options.deserialize || defaultDeserialize;
    const parsed = deserialize(item);

    // Validate with schema if provided
    if (options.schema) {
      return options.schema.parse(parsed);
    }

    return parsed;
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Storage parsing error');
    options.onError?.(err, key);
    return defaultValue;
  }
}

/**
 * Safe storage write
 */
function setStorageValue<T>(
  key: string,
  value: T,
  options: StorageOptions<T>,
  storageType: StorageType = 'local'
): void {
  if (!isBrowser) {
    return;
  }

  try {
    // Validate with schema if provided
    if (options.schema) {
      options.schema.parse(value);
    }

    const storage = getStorage(storageType);
    if (!storage) {
      return;
    }

    const serialize = options.serialize || defaultSerialize;
    const serialized = serialize(value);
    storage.setItem(key, serialized);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Storage write error');
    options.onError?.(err, key);
  }
}

/**
 * Base storage hook that works with both localStorage and sessionStorage
 */
function useBaseStorage<T>(
  key: string,
  defaultValue: T,
  storageType: StorageType,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  const {
    ssr = true,
    syncAcrossTabs = storageType === 'local' ? true : false, // Disable tab sync for sessionStorage
    onError,
  } = options;

  const [isLoading, setIsLoading] = useState(ssr);
  const [error, setError] = useState<Error | null>(null);
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (ssr) {
      return defaultValue;
    }
    return getStorageValue(key, defaultValue, options, storageType);
  });

  const optionsRef = useRef(options);
  optionsRef.current = options;

  // Initialize value on client side for SSR
  useEffect(() => {
    if (ssr && isBrowser) {
      try {
        const value = getStorageValue(key, defaultValue, optionsRef.current, storageType);
        setStoredValue(value);
        setError(null);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Storage initialization error');
        setError(error);
        onError?.(error, key);
      } finally {
        setIsLoading(false);
      }
    }
  }, [key, defaultValue, ssr, onError, storageType]);

  // Listen for storage changes across tabs (only for localStorage)
  useEffect(() => {
    if (!isBrowser || !syncAcrossTabs || storageType !== 'local') {
      return;
    }

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const deserialize = optionsRef.current.deserialize || defaultDeserialize;
          const newValue = deserialize(e.newValue);

          // Validate with schema if provided
          if (optionsRef.current.schema) {
            optionsRef.current.schema.parse(newValue);
          }

          setStoredValue(newValue);
          setError(null);
        } catch (err) {
          const error = err instanceof Error ? err : new Error('Storage sync error');
          setError(error);
          optionsRef.current.onError?.(error, key);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, syncAcrossTabs, storageType]);

  // Set value function
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      setStorageValue(key, valueToStore, optionsRef.current, storageType);
      setError(null);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Storage set error');
      setError(error);
      optionsRef.current.onError?.(error, key);
    }
  }, [key, storedValue, storageType]);

  // Remove value function
  const removeValue = useCallback(() => {
    try {
      setStoredValue(defaultValue);
      if (isBrowser) {
        const storage = getStorage(storageType);
        if (storage) {
          storage.removeItem(key);
        }
      }
      setError(null);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Storage remove error');
      setError(error);
      optionsRef.current.onError?.(error, key);
    }
  }, [key, defaultValue, storageType]);

  return {
    value: storedValue,
    setValue,
    removeValue,
    isLoading,
    error,
  };
}

/**
 * Local storage hook
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  return useBaseStorage(key, defaultValue, 'local', options);
}

/**
 * Session storage hook (uses sessionStorage with tab sync disabled)
 */
export function useSessionStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  return useBaseStorage(key, defaultValue, 'session', options);
}

/**
 * Typed local storage hook with schema validation
 */
export function useTypedStorage<T>(
  key: string,
  defaultValue: T,
  schema: z.ZodSchema<T>,
  options: Omit<StorageOptions<T>, 'schema'> = {}
): UseStorageReturn<T> {
  return useLocalStorage(key, defaultValue, { ...options, schema });
}

/**
 * Simple string storage hook
 */
export function useStringStorage(
  key: string,
  defaultValue: string = '',
  options: Omit<StorageOptions<string>, 'serialize' | 'deserialize'> = {}
): UseStorageReturn<string> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    serialize: (value) => value,
    deserialize: (value) => value,
  });
}

/**
 * Boolean storage hook
 */
export function useBooleanStorage(
  key: string,
  defaultValue: boolean = false,
  options: Omit<StorageOptions<boolean>, 'schema'> = {}
): UseStorageReturn<boolean> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema: z.boolean(),
  });
}

/**
 * Number storage hook
 */
export function useNumberStorage(
  key: string,
  defaultValue: number = 0,
  options: Omit<StorageOptions<number>, 'schema'> = {}
): UseStorageReturn<number> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema: z.number(),
  });
}

/**
 * Array storage hook
 */
export function useArrayStorage<T>(
  key: string,
  defaultValue: T[] = [],
  itemSchema?: z.ZodSchema<T>,
  options: Omit<StorageOptions<T[]>, 'schema'> = {}
): UseStorageReturn<T[]> {
  const schema = itemSchema ? z.array(itemSchema) : undefined;
  
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema,
  });
}

/**
 * Object storage hook
 */
export function useObjectStorage<T extends Record<string, any>>(
  key: string,
  defaultValue: T,
  schema?: z.ZodSchema<T>,
  options: Omit<StorageOptions<T>, 'schema'> = {}
): UseStorageReturn<T> {
  return useLocalStorage(key, defaultValue, {
    ...options,
    schema,
  });
}

/**
 * Storage hook with expiration
 */
export function useStorageWithExpiry<T>(
  key: string,
  defaultValue: T,
  expiryMinutes: number,
  options: StorageOptions<T> = {}
): UseStorageReturn<T> {
  const expiryKey = `${key}_expiry`;
  
  const isExpired = useCallback(() => {
    if (!isBrowser) return false;
    
    const expiry = localStorage.getItem(expiryKey);
    if (!expiry) return true;
    
    return Date.now() > parseInt(expiry, 10);
  }, [expiryKey]);

  const storage = useLocalStorage(key, defaultValue, {
    ...options,
    serialize: (value) => {
      // Set expiry when storing
      if (isBrowser) {
        const expiryTime = Date.now() + (expiryMinutes * 60 * 1000);
        localStorage.setItem(expiryKey, expiryTime.toString());
      }
      
      const serialize = options.serialize || defaultSerialize;
      return serialize(value);
    },
  });

  // Check expiry on mount and clear if expired
  const removeValueRef = useRef(storage.removeValue);
  removeValueRef.current = storage.removeValue;
  useEffect(() => {
    if (isExpired()) {
      removeValueRef.current();
      if (isBrowser) {
        localStorage.removeItem(expiryKey);
      }
    }
  }, [isExpired, storage, expiryKey]);

  return storage;
}
