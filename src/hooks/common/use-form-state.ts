/**
 * Standardized Form State Hook
 * Provides consistent form state management with validation, submission, and error handling
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { z, ZodObject } from 'zod';

/**
 * Form field error
 */
export interface FieldError {
  message: string;
  type: string;
}

/**
 * Form state
 */
export interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, FieldError>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValidating: boolean;
  isDirty: boolean;
  isValid: boolean;
  submitCount: number;
}

/**
 * Form configuration
 */
export interface FormConfig<T> {
  initialValues: T;
  validationSchema?: z.ZodSchema<T>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
  revalidateOnChange?: boolean;
  onSubmit?: (values: T) => Promise<void> | void;
  onValidationError?: (errors: Partial<Record<keyof T, FieldError>>) => void;
  onReset?: () => void;
}

/**
 * Form actions
 */
export interface FormActions<T> {
  setValue: (field: keyof T, value: T[keyof T]) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, error: FieldError) => void;
  setErrors: (errors: Partial<Record<keyof T, FieldError>>) => void;
  clearError: (field: keyof T) => void;
  clearErrors: () => void;
  setTouched: (field: keyof T, touched?: boolean) => void;
  setFieldTouched: (touched: Partial<Record<keyof T, boolean>>) => void;
  validateField: (field: keyof T) => Promise<boolean>;
  validateForm: () => Promise<boolean>;
  handleSubmit: (e?: React.FormEvent) => Promise<void>;
  reset: (values?: Partial<T>) => void;
  isDirtyField: (field: keyof T) => boolean;
  getFieldProps: (field: keyof T) => FieldProps<T[keyof T]>;
}

/**
 * Field props for form inputs
 */
export interface FieldProps<TValue> {
  name: string;
  value: TValue;
  onChange: (value: TValue) => void;
  onBlur: () => void;
  error?: FieldError;
  touched?: boolean;
  disabled?: boolean;
}

/**
 * Form hook return type
 */
export interface UseFormStateReturn<T> {
  state: FormState<T>;
  actions: FormActions<T>;
}

/**
 * Standardized form state hook
 */
export function useFormState<T extends Record<string, any>>(
  config: FormConfig<T>
): UseFormStateReturn<T> {
  const {
    initialValues,
    validationSchema,
    validateOnChange = true,
    validateOnBlur = true,
    validateOnSubmit = true,
    revalidateOnChange = true,
    onSubmit,
    onValidationError,
    onReset,
  } = config;

  // Form state
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, FieldError>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [submitCount, setSubmitCount] = useState(0);

  // Refs for tracking
  const initialValuesRef = useRef(initialValues);
  const mountedRef = useRef(true);

  // Update initial values ref when they change
  useEffect(() => {
    initialValuesRef.current = initialValues;
  }, [initialValues]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Computed state
  const isDirty = JSON.stringify(values) !== JSON.stringify(initialValuesRef.current);
  const isValid = Object.keys(errors).length === 0;

  const state: FormState<T> = {
    values,
    errors,
    touched,
    isSubmitting,
    isValidating,
    isDirty,
    isValid,
    submitCount,
  };

  // Validation function
  const validateField = useCallback(async (field: keyof T): Promise<boolean> => {
    if (!validationSchema) return true;

    try {
      setIsValidating(true);
      
      // Validate single field
      if (validationSchema instanceof ZodObject) {
        const fieldSchema = validationSchema.shape[field as string];
        if (fieldSchema) {
          await fieldSchema.parseAsync(values[field]);
        }
      } else {
        // For non-object schemas, validate the entire value
        await validationSchema.parseAsync(values);
      }
      
      // Clear error if validation passes
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
      
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors[0];
        setErrors(prev => ({
          ...prev,
          [field]: {
            message: fieldError.message,
            type: fieldError.code,
          },
        }));
      }
      return false;
    } finally {
      if (mountedRef.current) {
        setIsValidating(false);
      }
    }
  }, [validationSchema, values]);

  const validateForm = useCallback(async (): Promise<boolean> => {
    if (!validationSchema) return true;

    try {
      setIsValidating(true);
      await validationSchema.parseAsync(values);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Partial<Record<keyof T, FieldError>> = {};
        
        error.errors.forEach(err => {
          const field = err.path[0] as keyof T;
          if (field) {
            formErrors[field] = {
              message: err.message,
              type: err.code,
            };
          }
        });
        
        setErrors(formErrors);
        onValidationError?.(formErrors);
      }
      return false;
    } finally {
      if (mountedRef.current) {
        setIsValidating(false);
      }
    }
  }, [validationSchema, values, onValidationError]);

  // Actions
  const setValue = useCallback((field: keyof T, value: T[keyof T]) => {
    setValues(prev => ({ ...prev, [field]: value }));
    
    // Validate on change if enabled
    if (validateOnChange || (revalidateOnChange && errors[field])) {
      validateField(field);
    }
  }, [validateOnChange, revalidateOnChange, errors, validateField]);

  const setFormValues = useCallback((newValues: Partial<T>) => {
    setValues(prev => ({ ...prev, ...newValues }));
  }, []);

  const setError = useCallback((field: keyof T, error: FieldError) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  const setFormErrors = useCallback((newErrors: Partial<Record<keyof T, FieldError>>) => {
    setErrors(newErrors);
  }, []);

  const clearError = useCallback((field: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const setFieldTouched = useCallback((field: keyof T, isTouched = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }));
    
    // Validate on blur if enabled
    if (validateOnBlur && isTouched) {
      validateField(field);
    }
  }, [validateOnBlur, validateField]);

  const setAllTouched = useCallback((touchedFields: Partial<Record<keyof T, boolean>>) => {
    setTouched(touchedFields);
  }, []);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault();
    
    if (isSubmitting) return;
    
    setSubmitCount(prev => prev + 1);
    setIsSubmitting(true);
    
    try {
      // Mark all fields as touched
      const allTouched = Object.keys(values).reduce((acc, key) => {
        acc[key as keyof T] = true;
        return acc;
      }, {} as Partial<Record<keyof T, boolean>>);
      setTouched(allTouched);
      
      // Validate form if enabled
      if (validateOnSubmit) {
        const isFormValid = await validateForm();
        if (!isFormValid) {
          return;
        }
      }
      
      // Submit form
      if (onSubmit) {
        await onSubmit(values);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      
      // Handle submission errors
      if (error instanceof Error) {
        setErrors(prev => ({
          ...prev,
          _form: {
            message: error.message,
            type: 'submission_error',
          },
        }));
      }
    } finally {
      if (mountedRef.current) {
        setIsSubmitting(false);
      }
    }
  }, [isSubmitting, values, validateOnSubmit, validateForm, onSubmit]);

  const reset = useCallback((newValues?: Partial<T>) => {
    const resetValues = newValues ? { ...initialValues, ...newValues } : initialValues;
    setValues(resetValues);
    setErrors({});
    setTouched({});
    setSubmitCount(0);
    onReset?.();
  }, [initialValues, onReset]);

  const isDirtyField = useCallback((field: keyof T) => {
    return values[field] !== initialValuesRef.current[field];
  }, [values]);

  const getFieldProps = useCallback((field: keyof T): FieldProps<T[keyof T]> => {
    return {
      name: String(field),
      value: values[field],
      onChange: (value: T[keyof T]) => setValue(field, value),
      onBlur: () => setFieldTouched(field, true),
      error: errors[field],
      touched: touched[field],
      disabled: isSubmitting,
    };
  }, [values, errors, touched, isSubmitting, setValue, setFieldTouched]);

  const actions: FormActions<T> = {
    setValue,
    setValues: setFormValues,
    setError,
    setErrors: setFormErrors,
    clearError,
    clearErrors,
    setTouched: setFieldTouched,
    setFieldTouched: setAllTouched,
    validateField,
    validateForm,
    handleSubmit,
    reset,
    isDirtyField,
    getFieldProps,
  };

  return {
    state,
    actions,
  };
}

/**
 * Simplified form hook for basic use cases
 */
export function useSimpleForm<T extends Record<string, any>>(
  initialValues: T,
  onSubmit?: (values: T) => Promise<void> | void
) {
  return useFormState({
    initialValues,
    onSubmit,
    validateOnChange: false,
    validateOnBlur: false,
  });
}

/**
 * Form hook with Zod validation
 */
export function useValidatedForm<T extends Record<string, any>>(
  initialValues: T,
  schema: z.ZodSchema<T>,
  onSubmit?: (values: T) => Promise<void> | void
) {
  return useFormState({
    initialValues,
    validationSchema: schema,
    onSubmit,
  });
}
