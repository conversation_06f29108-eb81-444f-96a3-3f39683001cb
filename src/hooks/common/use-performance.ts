/**
 * Performance Monitoring Hooks
 * React hooks for tracking component and operation performance
 */

import React, { useEffect, useRef, useCallback, useMemo, useState } from 'react';
import { PerformanceMonitor, MetricType, PerformanceMetric } from '@/src/lib/services/performance/monitor';

// Global performance monitor instance
const performanceMonitor = new PerformanceMonitor({
  enabled: process.env.NODE_ENV !== 'test',
  sampling: {
    rate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    alwaysTrackSlow: true,
    alwaysTrackCritical: true,
  },
});

/**
 * Hook for tracking component render performance
 */
export function useRenderPerformance(
  componentName: string,
  dependencies: React.DependencyList = [],
  enabled: boolean = true
) {
  const operationIdRef = useRef<string | null>(null);
  const renderCountRef = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    renderCountRef.current++;
    
    // Start tracking render
    operationIdRef.current = performanceMonitor.startOperation(
      'component_render',
      componentName,
      {
        component: componentName,
      },
      {
        renderCount: renderCountRef.current,
        dependencies: dependencies.length,
      }
    );

    // End tracking after render
    const timeoutId = setTimeout(() => {
      if (operationIdRef.current) {
        performanceMonitor.endOperation(operationIdRef.current);
        operationIdRef.current = null;
      }
    }, 0);

    return () => {
      clearTimeout(timeoutId);
      if (operationIdRef.current) {
        performanceMonitor.endOperation(operationIdRef.current);
        operationIdRef.current = null;
      }
    };
  }, dependencies);

  return {
    renderCount: renderCountRef.current,
  };
}

/**
 * Hook for tracking async operations
 */
export function useOperationPerformance() {
  const activeOperations = useRef<Map<string, string>>(new Map());

  const startOperation = useCallback((
    type: MetricType,
    name: string,
    context?: PerformanceMetric['context'],
    metadata?: Record<string, unknown>
  ): string => {
    const key = `${type}_${name}`;
    
    // End previous operation with same key if exists
    const existingOperationId = activeOperations.current.get(key);
    if (existingOperationId) {
      performanceMonitor.endOperation(existingOperationId);
    }

    const operationId = performanceMonitor.startOperation(type, name, context, metadata);
    activeOperations.current.set(key, operationId);
    
    return operationId;
  }, []);

  const endOperation = useCallback((operationId: string): PerformanceMetric | null => {
    // Remove from active operations
    for (const [key, id] of activeOperations.current.entries()) {
      if (id === operationId) {
        activeOperations.current.delete(key);
        break;
      }
    }

    return performanceMonitor.endOperation(operationId);
  }, []);

  const endOperationByKey = useCallback((type: MetricType, name: string): PerformanceMetric | null => {
    const key = `${type}_${name}`;
    const operationId = activeOperations.current.get(key);
    
    if (operationId) {
      activeOperations.current.delete(key);
      return performanceMonitor.endOperation(operationId);
    }
    
    return null;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // End all active operations
      for (const operationId of activeOperations.current.values()) {
        performanceMonitor.endOperation(operationId);
      }
      activeOperations.current.clear();
    };
  }, []);

  return {
    startOperation,
    endOperation,
    endOperationByKey,
    activeOperationsCount: activeOperations.current.size,
  };
}

/**
 * Hook for tracking API request performance
 */
export function useAPIPerformance() {
  const { startOperation, endOperation } = useOperationPerformance();

  const trackRequest = useCallback(async <T>(
    endpoint: string,
    method: string,
    requestFn: () => Promise<T>,
    context?: Partial<PerformanceMetric['context']>
  ): Promise<T> => {
    const operationId = startOperation(
      'api_request',
      `${method} ${endpoint}`,
      {
        endpoint,
        method,
        ...context,
      }
    );

    try {
      const result = await requestFn();
      endOperation(operationId);
      return result;
    } catch (error) {
      const metric = endOperation(operationId);
      if (metric) {
        // Add error information to metric
        metric.metadata = {
          ...metric.metadata,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        };
      }
      throw error;
    }
  }, [startOperation, endOperation]);

  return { trackRequest };
}

/**
 * Hook for tracking user interactions
 */
export function useInteractionPerformance() {
  const { startOperation, endOperation } = useOperationPerformance();

  const trackInteraction = useCallback((
    interactionName: string,
    handler: () => void | Promise<void>,
    context?: Partial<PerformanceMetric['context']>
  ) => {
    return async (event?: React.SyntheticEvent) => {
      const operationId = startOperation(
        'user_interaction',
        interactionName,
        {
          ...context,
          operation: interactionName,
        }
      );

      try {
        await handler();
        endOperation(operationId);
      } catch (error) {
        const metric = endOperation(operationId);
        if (metric) {
          metric.metadata = {
            ...metric.metadata,
            error: error instanceof Error ? error.message : 'Unknown error',
            success: false,
          };
        }
        throw error;
      }
    };
  }, [startOperation, endOperation]);

  return { trackInteraction };
}

/**
 * Hook for tracking page load performance
 */
export function usePageLoadPerformance(pageName: string, enabled: boolean = true) {
  const hasTracked = useRef(false);

  useEffect(() => {
    if (!enabled || hasTracked.current) return;

    hasTracked.current = true;

    // Track page load using Navigation Timing API
    if (typeof window !== 'undefined' && window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      const loadTime = timing.loadEventEnd - timing.navigationStart;
      const domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
      const firstPaint = timing.responseEnd - timing.navigationStart;

      performanceMonitor.recordMetric({
        id: `page_load_${Date.now()}`,
        type: 'page_load',
        name: pageName,
        duration: loadTime,
        timestamp: new Date(),
        context: {
          component: pageName,
          operation: 'page_load',
        },
        details: {
          startTime: timing.navigationStart,
          endTime: timing.loadEventEnd,
        },
        tags: ['page_load', pageName],
        severity: loadTime > 5000 ? 'critical' : loadTime > 2000 ? 'slow' : 'normal',
        metadata: {
          domContentLoaded,
          firstPaint,
          navigationStart: timing.navigationStart,
          loadEventEnd: timing.loadEventEnd,
        },
      });
    }
  }, [pageName, enabled]);

  return {
    hasTracked: hasTracked.current,
  };
}

/**
 * Hook for getting performance statistics
 */
export function usePerformanceStats(
  timeRangeMinutes: number = 60,
  type?: MetricType,
  refreshInterval?: number
) {
  const [stats, setStats] = useState(() => 
    performanceMonitor.getStats(timeRangeMinutes, type)
  );

  useEffect(() => {
    const updateStats = () => {
      setStats(performanceMonitor.getStats(timeRangeMinutes, type));
    };

    if (refreshInterval) {
      const interval = setInterval(updateStats, refreshInterval);
      return () => clearInterval(interval);
    } else {
      updateStats();
    }
  }, [timeRangeMinutes, type, refreshInterval]);

  return stats;
}

/**
 * Hook for performance monitoring configuration
 */
export function usePerformanceConfig() {
  const updateConfig = useCallback((config: Parameters<typeof performanceMonitor.updateConfig>[0]) => {
    performanceMonitor.updateConfig(config);
  }, []);

  const getMetrics = useCallback((type?: MetricType, limit?: number) => {
    return performanceMonitor.getMetrics(type, limit);
  }, []);

  const clearMetrics = useCallback(() => {
    performanceMonitor.clearMetrics();
  }, []);

  return {
    updateConfig,
    getMetrics,
    clearMetrics,
  };
}

/**
 * Higher-order component for automatic render performance tracking
 */
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = (props: P) => {
    const displayName = componentName || Component.displayName || Component.name || 'Component';

    useRenderPerformance(displayName, [props]);

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPerformanceTracking(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Performance measurement decorator for functions
 */
export function measurePerformance<T extends (...args: any[]) => any>(
  fn: T,
  name: string,
  type: MetricType = 'custom'
): T {
  return ((...args: Parameters<T>) => {
    const operationId = performanceMonitor.startOperation(type, name);
    
    try {
      const result = fn(...args);
      
      // Handle async functions
      if (result instanceof Promise) {
        return result.finally(() => {
          performanceMonitor.endOperation(operationId);
        });
      }
      
      performanceMonitor.endOperation(operationId);
      return result;
    } catch (error) {
      performanceMonitor.endOperation(operationId);
      throw error;
    }
  }) as T;
}

// Export the performance monitor instance for direct access
export { performanceMonitor };
