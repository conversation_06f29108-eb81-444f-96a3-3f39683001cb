/**
 * Standardized API Query Hook
 * Provides consistent data fetching with error handling, caching, and retry logic
 */

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { apiClient } from '@/src/lib/api/client';
import { APIResponse, ErrorAPIResponse, PlatyfendError } from '@/src/lib/types/api';

/**
 * Query configuration options
 */
export interface APIQueryOptions<TData, TError = ErrorAPIResponse> extends Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'> {
  // Custom retry logic
  retryOnAuthError?: boolean;
  retryOnClientError?: boolean;
  maxRetries?: number;
  
  // Cache configuration
  staleTimeMinutes?: number;
  cacheTimeMinutes?: number;
  
  // Conditional execution
  enabled?: boolean;
  dependencies?: (string | number | boolean | null | undefined)[];
}

/**
 * Mutation context type for optimistic updates
 */
interface MutationContext {
  previousData?: unknown;
}

/**
 * Mutation configuration options
 */
export interface APIMutationOptions<TData, TVariables, TError = ErrorAPIResponse>
  extends Omit<UseMutationOptions<TData, TError, TVariables, unknown>, 'mutationFn'> {
  // Invalidation options
  invalidateQueries?: string[][];
  invalidateAll?: boolean;

  // Optimistic updates
  optimisticUpdate?: {
    queryKey: string[];
    updater: (oldData: unknown, variables: TVariables) => unknown;
  };
}

/**
 * Standard API query hook
 */
export function useAPIQuery<TData = unknown, TError = ErrorAPIResponse>(
  queryKey: (string | number | boolean | null | undefined)[],
  endpoint: string,
  options: APIQueryOptions<TData, TError> = {}
) {
  const {
    retryOnAuthError = false,
    retryOnClientError = false,
    maxRetries = 3,
    staleTimeMinutes = 5,
    cacheTimeMinutes = 30,
    dependencies = [],
    ...queryOptions
  } = options;

  // Filter out null/undefined values from query key
  const filteredQueryKey = queryKey.filter(key => key !== null && key !== undefined);
  
  // Check if all dependencies are available
  const hasAllDependencies = dependencies.every(dep => dep !== null && dep !== undefined);
  const isEnabled = (options.enabled ?? true) && hasAllDependencies;

  return useQuery<TData, TError>({
    queryKey: filteredQueryKey,
    queryFn: async () => {
      try {
        const response = await apiClient.get<TData>(endpoint);
        return response.data;
      } catch (error) {
        // Handle API client errors
        if (error && typeof error === 'object' && 'type' in error) {
          throw error;
        }

        // Handle other errors
        throw {
          success: false,
          error: 'API request failed',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          timestamp: new Date().toISOString(),
        } as TError;
      }
    },
    staleTime: staleTimeMinutes * 60 * 1000,
    gcTime: cacheTimeMinutes * 60 * 1000, // Updated from cacheTime to gcTime
    enabled: isEnabled,
    retry: (failureCount, error) => {
      // Don't retry if max retries exceeded
      if (failureCount >= maxRetries) {
        return false;
      }

      // Handle authentication errors
      if (!retryOnAuthError && error && typeof error === 'object' && error !== null && 'error' in error &&
          typeof error.error === 'string' && error.error.includes('Not authenticated')) {
        return false;
      }

      // Handle client errors (4xx)
      if (!retryOnClientError && error && typeof error === 'object' && error !== null && 'type' in error &&
          error.type === 'external_api_error' && 'statusCode' in error &&
          typeof error.statusCode === 'number' &&
          error.statusCode >= 400 && error.statusCode < 500) {
        return false;
      }

      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...queryOptions,
  });
}

/**
 * Standard API mutation hook
 */
export function useAPIMutation<TData = unknown, TVariables = unknown, TError = ErrorAPIResponse>(
  endpoint: string,
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST',
  options: APIMutationOptions<TData, TVariables, TError> = {}
) {
  const queryClient = useQueryClient();
  const {
    invalidateQueries = [],
    invalidateAll = false,
    optimisticUpdate,
    ...mutationOptions
  } = options;

  return useMutation<TData, TError, TVariables>({
    mutationFn: async (variables: TVariables) => {
      try {
        let response;
        
        switch (method) {
          case 'POST':
            response = await apiClient.post<TData>(endpoint, variables);
            break;
          case 'PUT':
            response = await apiClient.put<TData>(endpoint, variables);
            break;
          case 'PATCH':
            response = await apiClient.patch<TData>(endpoint, variables);
            break;
          case 'DELETE':
            response = await apiClient.delete<TData>(endpoint);
            break;
          default:
            throw new Error(`Unsupported method: ${method}`);
        }
        
        return response.data;
      } catch (error) {
        if (error && typeof error === 'object' && 'type' in error) {
          throw error;
        }

        throw {
          success: false,
          error: 'API mutation failed',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          timestamp: new Date().toISOString(),
        } as TError;
      }
    },
    onMutate: async (variables) => {
      // Handle optimistic updates
      if (optimisticUpdate) {
        await queryClient.cancelQueries({ queryKey: optimisticUpdate.queryKey });
        
        const previousData = queryClient.getQueryData(optimisticUpdate.queryKey);
        
        queryClient.setQueryData(
          optimisticUpdate.queryKey,
          optimisticUpdate.updater(previousData, variables)
        );
        
        return { previousData };
      }
      
      return mutationOptions.onMutate?.(variables);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic updates
      const mutationContext = context as MutationContext | undefined;
      if (optimisticUpdate && mutationContext?.previousData) {
        queryClient.setQueryData(optimisticUpdate.queryKey, mutationContext.previousData);
      }

      mutationOptions.onError?.(error, variables, context);
    },
    onSuccess: (data, variables, context) => {
      // Invalidate queries
      if (invalidateAll) {
        queryClient.invalidateQueries();
      } else {
        invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      
      mutationOptions.onSuccess?.(data, variables, context);
    },
    ...mutationOptions,
  });
}

/**
 * Paginated query hook
 */
export function usePaginatedQuery<TData = unknown, TError = ErrorAPIResponse>(
  queryKey: (string | number | boolean | null | undefined)[],
  endpoint: string,
  page: number = 1,
  limit: number = 20,
  options: APIQueryOptions<TData, TError> = {}
) {
  const paginatedEndpoint = `${endpoint}?page=${page}&limit=${limit}`;
  const paginatedQueryKey = [...queryKey, 'paginated', page, limit];
  
  return useAPIQuery<TData, TError>(paginatedQueryKey, paginatedEndpoint, {
    ...options,
    placeholderData: (previousData) => previousData, // Keep previous page data while loading new page
  });
}

/**
 * Infinite query hook for infinite scrolling
 */
export function useInfiniteAPIQuery<TData = unknown, TError = ErrorAPIResponse>(
  queryKey: (string | number | boolean | null | undefined)[],
  endpoint: string,
  options: APIQueryOptions<TData, TError> = {}
) {
  // This would use useInfiniteQuery from React Query
  // Implementation would depend on your API's pagination structure
  // For now, returning a placeholder
  return useAPIQuery<TData, TError>(queryKey, endpoint, options);
}

/**
 * Query with real-time updates (polling)
 */
export function useRealtimeQuery<TData = unknown, TError = ErrorAPIResponse>(
  queryKey: (string | number | boolean | null | undefined)[],
  endpoint: string,
  intervalSeconds: number = 30,
  options: APIQueryOptions<TData, TError> = {}
) {
  return useAPIQuery<TData, TError>(queryKey, endpoint, {
    ...options,
    refetchInterval: intervalSeconds * 1000,
    refetchIntervalInBackground: false,
  });
}

/**
 * Dependent query hook - waits for dependencies before executing
 */
export function useDependentQuery<TData = unknown, TError = ErrorAPIResponse>(
  queryKey: (string | number | boolean | null | undefined)[],
  endpoint: string,
  dependencies: (string | number | boolean | null | undefined)[],
  options: APIQueryOptions<TData, TError> = {}
) {
  return useAPIQuery<TData, TError>(queryKey, endpoint, {
    ...options,
    dependencies,
  });
}

/**
 * Query with automatic refresh on window focus
 */
export function useFreshQuery<TData = unknown, TError = ErrorAPIResponse>(
  queryKey: (string | number | boolean | null | undefined)[],
  endpoint: string,
  options: APIQueryOptions<TData, TError> = {}
) {
  return useAPIQuery<TData, TError>(queryKey, endpoint, {
    ...options,
    refetchOnWindowFocus: true,
    staleTimeMinutes: 0, // Always consider data stale
  });
}
