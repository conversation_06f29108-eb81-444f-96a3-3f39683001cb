/**
 * Common Hooks Index
 * Exports all standardized, reusable hooks
 */

// API hooks
export {
  useAPIQuery,
  useAPIMutation,
  usePaginatedQuery,
  useInfiniteAPIQuery,
  useRealtimeQuery,
  useDependentQuery,
  useFreshQuery,
  type APIQueryOptions,
  type APIMutationOptions
} from './use-api-query';

// Form hooks
export {
  useFormState,
  useSimpleForm,
  useValidatedForm,
  type FormState,
  type FormConfig,
  type FormActions,
  type FieldProps,
  type FieldError,
  type UseFormStateReturn
} from './use-form-state';

// Storage hooks
export {
  useLocalStorage,
  useSessionStorage,
  useTypedStorage,
  useStringStorage,
  useBooleanStorage,
  useNumberStorage,
  useArrayStorage,
  useObjectStorage,
  useStorageWithExpiry,
  type StorageOptions,
  type UseStorageReturn
} from './use-local-storage';

// Async operation hooks
export {
  useAsync,
  useAsyncEffect,
  useDebouncedAsync,
  useAsyncWithRetry,
  type AsyncState,
  type AsyncOptions,
  type UseAsyncReturn
} from './use-async';

// Re-export commonly used React Query hooks for convenience
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery
} from '@tanstack/react-query';
