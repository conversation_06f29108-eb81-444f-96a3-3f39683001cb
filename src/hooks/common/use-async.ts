/**
 * Standardized Async Operation Hook
 * Provides consistent async operation handling with loading states, error handling, and cancellation
 */

import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Async operation state
 */
export interface AsyncState<T, E = Error> {
  data: T | null;
  error: E | null;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  isIdle: boolean;
}

/**
 * Async operation options
 */
export interface AsyncOptions<T, E = Error> {
  // Initial data
  initialData?: T;
  
  // Error handling
  onError?: (error: E) => void;
  onSuccess?: (data: T) => void;
  
  // Retry configuration
  retries?: number;
  retryDelay?: number;
  retryCondition?: (error: E, attempt: number) => boolean;
  
  // Cancellation
  abortOnUnmount?: boolean;
  
  // Debouncing
  debounceMs?: number;
}

/**
 * Async operation return type
 */
export interface UseAsyncReturn<T, P extends any[] = [], E = Error> {
  // State
  data: T | null;
  error: E | null;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  isIdle: boolean;
  
  // Actions
  execute: (...params: P) => Promise<T>;
  reset: () => void;
  cancel: () => void;
  
  // Utilities
  retry: () => Promise<T>;
  setData: (data: T) => void;
  setError: (error: E) => void;
}

/**
 * Main async hook
 */
export function useAsync<T, P extends any[] = [], E = Error>(
  asyncFunction: (...params: P) => Promise<T>,
  options: AsyncOptions<T, E> = {}
): UseAsyncReturn<T, P, E> {
  const {
    initialData = null,
    onError,
    onSuccess,
    retries = 0,
    retryDelay = 1000,
    retryCondition,
    abortOnUnmount = true,
    debounceMs = 0,
  } = options;

  // State
  const [state, setState] = useState<AsyncState<T, E>>({
    data: initialData,
    error: null,
    isLoading: false,
    isSuccess: false,
    isError: false,
    isIdle: true,
  });

  // Refs for tracking
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastParamsRef = useRef<P | null>(null);
  const retryCountRef = useRef(0);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (abortOnUnmount && abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [abortOnUnmount]);

  // Execute function with retry logic
  const executeWithRetry = useCallback(async (...params: P): Promise<T> => {
    const attempt = retryCountRef.current;
    
    try {
      // Create new abort controller for this execution
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      // Execute the async function
      const result = await asyncFunction(...params);
      
      if (!mountedRef.current) return result;

      // Success
      setState(prev => ({
        ...prev,
        data: result,
        error: null,
        isLoading: false,
        isSuccess: true,
        isError: false,
        isIdle: false,
      }));

      retryCountRef.current = 0;
      onSuccess?.(result);
      
      return result;
    } catch (error) {
      if (!mountedRef.current) throw error;

      const typedError = error as E;
      
      // Check if we should retry
      const shouldRetry = attempt < retries && 
        (!retryCondition || retryCondition(typedError, attempt));
      
      if (shouldRetry) {
        retryCountRef.current = attempt + 1;
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
        
        if (!mountedRef.current) throw error;
        
        // Retry
        return executeWithRetry(...params);
      }

      // Final error state
      setState(prev => ({
        ...prev,
        data: prev.data, // Keep previous data
        error: typedError,
        isLoading: false,
        isSuccess: false,
        isError: true,
        isIdle: false,
      }));

      retryCountRef.current = 0;
      onError?.(typedError);
      
      throw error;
    }
  }, [asyncFunction, retries, retryDelay, retryCondition, onSuccess, onError]);

  // Main execute function with debouncing
  const execute = useCallback(async (...params: P): Promise<T> => {
    lastParamsRef.current = params;

    // Clear existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set loading state immediately if no debounce
    if (debounceMs === 0) {
      setState(prev => ({
        ...prev,
        isLoading: true,
        isError: false,
        isIdle: false,
      }));
    }

    return new Promise((resolve, reject) => {
      const executeFunction = async () => {
        if (!mountedRef.current) {
          reject(new Error('Component unmounted'));
          return;
        }

        // Set loading state
        setState(prev => ({
          ...prev,
          isLoading: true,
          isError: false,
          isIdle: false,
        }));

        try {
          const result = await executeWithRetry(...params);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      if (debounceMs > 0) {
        debounceTimeoutRef.current = setTimeout(executeFunction, debounceMs);
      } else {
        executeFunction();
      }
    });
  }, [executeWithRetry, debounceMs]);

  // Reset function
  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    setState({
      data: initialData,
      error: null,
      isLoading: false,
      isSuccess: false,
      isError: false,
      isIdle: true,
    });

    retryCountRef.current = 0;
    lastParamsRef.current = null;
  }, [initialData]);

  // Cancel function
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    setState(prev => ({
      ...prev,
      isLoading: false,
    }));
  }, []);

  // Retry function
  const retry = useCallback(async (): Promise<T> => {
    if (!lastParamsRef.current) {
      throw new Error('No previous execution to retry');
    }
    
    retryCountRef.current = 0;
    return execute(...lastParamsRef.current);
  }, [execute]);

  // Set data function
  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data,
      error: null,
      isSuccess: true,
      isError: false,
      isIdle: false,
    }));
  }, []);

  // Set error function
  const setError = useCallback((error: E) => {
    setState(prev => ({
      ...prev,
      error,
      isSuccess: false,
      isError: true,
      isIdle: false,
    }));
  }, []);

  return {
    // State
    data: state.data,
    error: state.error,
    isLoading: state.isLoading,
    isSuccess: state.isSuccess,
    isError: state.isError,
    isIdle: state.isIdle,
    
    // Actions
    execute,
    reset,
    cancel,
    
    // Utilities
    retry,
    setData,
    setError,
  };
}

/**
 * Async hook that executes immediately
 */
export function useAsyncEffect<T, E = Error>(
  asyncFunction: () => Promise<T>,
  dependencies: React.DependencyList = [],
  options: AsyncOptions<T, E> = {}
): Omit<UseAsyncReturn<T, [], E>, 'execute'> {
  const asyncState = useAsync(asyncFunction, options);

  useEffect(() => {
    asyncState.execute();
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    data: asyncState.data,
    error: asyncState.error,
    isLoading: asyncState.isLoading,
    isSuccess: asyncState.isSuccess,
    isError: asyncState.isError,
    isIdle: asyncState.isIdle,
    reset: asyncState.reset,
    cancel: asyncState.cancel,
    retry: asyncState.retry,
    setData: asyncState.setData,
    setError: asyncState.setError,
  };
}

/**
 * Debounced async hook
 */
export function useDebouncedAsync<T, P extends any[] = [], E = Error>(
  asyncFunction: (...params: P) => Promise<T>,
  debounceMs: number,
  options: Omit<AsyncOptions<T, E>, 'debounceMs'> = {}
): UseAsyncReturn<T, P, E> {
  return useAsync(asyncFunction, { ...options, debounceMs });
}

/**
 * Async hook with automatic retry
 */
export function useAsyncWithRetry<T, P extends any[] = [], E = Error>(
  asyncFunction: (...params: P) => Promise<T>,
  retries: number = 3,
  retryDelay: number = 1000,
  options: Omit<AsyncOptions<T, E>, 'retries' | 'retryDelay'> = {}
): UseAsyncReturn<T, P, E> {
  return useAsync(asyncFunction, { ...options, retries, retryDelay });
}
