import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { postSecurityReview } from '@/src/lib/services/github-comments';
import { SecurityReviewPayload } from '@/src/lib/types/security-review';
import { env } from '@/src/lib/config/environment';

// Validation schemas
const CommentSchema = z.object({
  body: z.string(),
  file_path: z.string().optional(),
  line: z.number().optional(),
  severity: z.string().optional(),
  comment_type: z.string(),
  finding_ids: z.array(z.string()).optional(),
});

const AnalysisSummarySchema = z.object({
  total_findings: z.number(),
  critical_findings: z.number(),
  high_findings: z.number(),
  success: z.boolean(),
  analyzers_run: z.number(),
});

const PRInfoSchema = z.object({
  id: z.number(),
  number: z.number(),
  title: z.string(),
  repository: z.string(),
  author: z.string(),
  url: z.string(),
  base_branch: z.string(),
  head_branch: z.string(),
  action: z.string(),
});

const SecurityReviewPayloadSchema = z.object({
  pr_info: PRInfoSchema,
  analysis_summary: AnalysisSummarySchema,
  comments: z.array(CommentSchema),
  timestamp: z.number(),
});

/**
 * POST /api/security-review
 * Receives security review data from Python backend and posts comments to GitHub PR
 */
export async function POST(request: NextRequest) {
  try {
    // Verify API key for backend authentication
    const apiKey = request.headers.get('x-api-key');

    if (!apiKey || apiKey !== env.PLATYFEND_API_KEY) {
      console.error('Invalid or missing API key for security review endpoint');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = SecurityReviewPayloadSchema.safeParse(body);
    
    if (!validationResult.success) {
      console.error('Invalid security review payload:', validationResult.error.errors);
      return NextResponse.json(
        { 
          error: 'Invalid payload format',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { pr_info, analysis_summary, comments, timestamp } = validationResult.data;

    console.log(`Security review received for PR #${pr_info.number} in ${pr_info.repository}`, {
      totalFindings: analysis_summary.total_findings,
      criticalFindings: analysis_summary.critical_findings,
      highFindings: analysis_summary.high_findings,
      success: analysis_summary.success,
      commentsCount: comments.length,
      timestamp: new Date(timestamp * 1000).toISOString()
    });

    // Process the security review and post comments
    // The SHA will be automatically fetched from the PR if not provided
    const result = await postSecurityReview(
      pr_info,
      analysis_summary,
      comments
    );

    if (result.success) {
      console.log(`Security review comments posted successfully for PR #${pr_info.number}`);
      return NextResponse.json({
        success: true,
        message: 'Security review comments posted successfully',
        data: {
          pr_number: pr_info.number,
          repository: pr_info.repository,
          total_findings: analysis_summary.total_findings,
          comments_posted: comments.length,
          errors: result.errors
        }
      });
    } else {
      console.error(`Failed to post security review comments for PR #${pr_info.number}:`, result.errors);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to post some or all comments',
          details: result.errors,
          data: {
            pr_number: pr_info.number,
            repository: pr_info.repository,
            total_findings: analysis_summary.total_findings,
            comments_attempted: comments.length
          }
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Security review API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/security-review
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    endpoint: 'security-review',
    message: 'Security review endpoint is operational',
    timestamp: new Date().toISOString()
  });
}
