import { getServerSession } from "next-auth/next";
import { authOptions } from "@/src/features/auth/lib/auth-config";
import { NextResponse } from "next/server";
import { databaseService } from "@/src/lib/database/service";
import { Organization } from "@/src/lib/database/models";
import { OrganizationsAPIResponse, ErrorAPIResponse } from "@/src/lib/types/api";

export async function GET(): Promise<NextResponse<OrganizationsAPIResponse | ErrorAPIResponse>> {
  const timestamp = new Date().toISOString();

  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({
        success: false,
        error: "Not authenticated",
        message: "User must be logged in to access organizations",
        timestamp,
      } as ErrorAPIResponse, { status: 401 });
    }

    // Connect to database using the centralized service
    console.log('🔄 Connecting to database for organizations API...');
    await databaseService.ensureConnection();
    console.log('✅ Database connection established');

    // Fetch user organizations from MongoDB with optimized query
    console.log('🔍 Fetching user organizations for user:', session.user.id);

    const dbOrganizations = await Organization.find({
      user_id: session.user.id
    })
    .select('org_id org_name provider avatar_url org_type description public_repos installation_status repos')
    .lean()
    .maxTimeMS(30000) // MongoDB timeout
    .hint({ user_id: 1, provider: 1 }) // Use the compound index
    .exec();

    console.log('✅ Found', dbOrganizations.length, 'organizations in database');

    const errors: Array<{ provider: string; error: string }> = [];

    // Convert database organizations to API format
    const organizations = dbOrganizations.map(org => ({
      id: org.org_id, // Use the external org_id (GitHub user/org ID)
      name: org.org_name,
      provider: org.provider as 'github' | 'gitlab',
      avatar: org.avatar_url,
      isCurrent: true, // For now, mark all as current - can be refined later
      type: org.org_type as 'personal' | 'organization',
      description: org.description,
      publicRepos: org.public_repos,
      installationStatus: org.installation_status as 'active' | 'pending' | 'suspended' | 'deleted',
      repoCount: org.repos?.length || 0
    }));

    // Check what providers are missing (simplified for now)
    const missingProviders: string[] = [];
    const hasGitHub = dbOrganizations.some(org => org.provider === 'github');
    const hasGitLab = dbOrganizations.some(org => org.provider === 'gitlab');

    if (!hasGitHub) missingProviders.push('github');
    if (!hasGitLab) missingProviders.push('gitlab');

    const response: OrganizationsAPIResponse = {
      organizations,
      totalCount: organizations.length,
      missingProviders,
      errors: errors.length > 0 ? errors : undefined,
      user: {
        id: session.user.id,
        email: session.user.email || undefined,
        name: session.user.name || undefined
      },
      timestamp,
    };

    return NextResponse.json(response);

  } catch (error: unknown) {
    console.error("Organizations API error:", error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorName = error instanceof Error ? error.name : 'UnknownError';

    // Check if it's a timeout or connection error
    const isTimeoutError = errorMessage.includes('timeout') ||
                          errorMessage.includes('buffering') ||
                          errorName === 'MongooseError';

    // For timeout errors, return a more specific response
    if (isTimeoutError) {
      const response: ErrorAPIResponse = {
        success: false,
        error: "Database connection timeout",
        message: "The request took too long to process. Please try again.",
        timestamp,
        details: {
          isTimeout: true,
          errorName,
          ...(process.env.NODE_ENV === 'development' && {
            stack: error instanceof Error ? error.stack : undefined
          })
        }
      };
      return NextResponse.json(response, { status: 503 });
    }

    // General error response
    const response: ErrorAPIResponse = {
      success: false,
      error: "Failed to fetch organizations",
      message: "An error occurred while fetching organizations. Please try again.",
      timestamp,
      details: {
        errorName,
        ...(process.env.NODE_ENV === 'development' && {
          message: errorMessage,
          stack: error instanceof Error ? error.stack : undefined
        })
      }
    };

    return NextResponse.json(response, { status: 500 });
  }
}
