import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/src/features/auth/lib/auth-config';
import { 
  githubStatusService, 
  GitHubStatusState,
  setPendingStatusForPR,
  setSuccessStatusForPR,
  setFailureStatusForPR
} from '@/src/lib/services/github-status';

/**
 * API endpoint for manually testing GitHub status checks
 * POST /api/github/status
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { owner, repo, sha, prNumber, state, issuesFound, errorMessage } = body;

    // Validate required fields
    if (!owner || !repo || !sha || !prNumber) {
      return NextResponse.json(
        { error: 'Missing required fields: owner, repo, sha, prNumber' },
        { status: 400 }
      );
    }

    let success = false;
    let message = '';

    switch (state) {
      case 'pending':
        success = await setPendingStatusForPR(owner, repo, sha, prNumber);
        message = 'Pending status set';
        break;
      
      case 'success':
        success = await setSuccessStatusForPR(owner, repo, sha, prNumber, issuesFound || 0);
        message = 'Success status set';
        break;
      
      case 'failure':
        success = await setFailureStatusForPR(owner, repo, sha, prNumber, errorMessage);
        message = 'Failure status set';
        break;
      
      default:
        return NextResponse.json(
          { error: 'Invalid state. Must be: pending, success, or failure' },
          { status: 400 }
        );
    }

    if (success) {
      return NextResponse.json({
        success: true,
        message,
        data: { owner, repo, sha, prNumber, state }
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to set GitHub status' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('GitHub status API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check if a repository has an installation
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const owner = searchParams.get('owner');
    const repo = searchParams.get('repo');

    if (!owner || !repo) {
      return NextResponse.json(
        { error: 'Missing required parameters: owner, repo' },
        { status: 400 }
      );
    }

    const installationId = await githubStatusService.getInstallationIdForRepo(owner, repo);

    return NextResponse.json({
      hasInstallation: !!installationId,
      installationId: installationId || null,
      repository: `${owner}/${repo}`
    });

  } catch (error) {
    console.error('GitHub status check API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
