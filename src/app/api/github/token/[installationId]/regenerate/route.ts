import { NextRequest, NextResponse } from 'next/server';
import { githubAppAuth } from '@/src/lib/github/app-auth';
import { Organization, InstallationStatus } from '@/src/lib/database/models';
import connectToDatabase from '@/src/lib/database/mongoose';

/**
 * POST /api/github/token/[installationId]/regenerate
 * Force regenerate installation token for Python backend
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ installationId: string }> }
) {
  try {
    const { installationId } = await params;

    if (!installationId) {
      return NextResponse.json(
        { error: 'Installation ID is required' },
        { status: 400 }
      );
    }

    // Verify the installation exists and is active
    await connectToDatabase();
    const organization = await Organization.findOne({
      installation_id: installationId,
      installation_status: InstallationStatus.ACTIVE
    });

    if (!organization) {
      return NextResponse.json(
        { error: 'Installation not found or not active' },
        { status: 404 }
      );
    }

    // Force regenerate token
    const newToken = await githubAppAuth.regenerateInstallationToken(installationId);

    return NextResponse.json({
      success: true,
      installation_id: installationId,
      token: newToken,
      expires_in_hours: 1,
      organization: {
        name: organization.org_name,
        type: organization.org_type,
        provider: organization.provider
      },
      regenerated_at: new Date().toISOString(),
      message: 'Token successfully regenerated'
    });

  } catch (error) {
    console.error('Error regenerating installation token:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to regenerate installation token',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
