import { NextResponse } from "next/server";
import { databaseService } from "@/src/lib/database/service";
import { HealthCheckResponse } from "@/src/lib/types/api";

export async function GET(): Promise<NextResponse<HealthCheckResponse>> {
  const timestamp = new Date().toISOString();

  try {
    const dbHealth = await databaseService.getHealth();

    const health: HealthCheckResponse = {
      status: dbHealth.status === 'healthy' ? 'ok' : 'error',
      timestamp,
      database: {
        status: dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
        message: dbHealth.message,
      },
      environment: process.env.NODE_ENV || 'unknown',
    };

    const statusCode = dbHealth.status === 'healthy' ? 200 : 503;

    return NextResponse.json(health, { status: statusCode });
  } catch (error) {
    const health: HealthCheckResponse = {
      status: 'error',
      timestamp,
      database: {
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      environment: process.env.NODE_ENV || 'unknown',
    };

    return NextResponse.json(health, { status: 503 });
  }
}
