/**
 * Standardized Data List Component
 * Reusable list component with filtering, sorting, pagination, and loading states
 */

import React, { useState, useMemo } from 'react';
import { Input } from '@/src/components/ui/input';
import { Button } from '@/src/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/src/components/ui/select';
import { Badge } from '@/src/components/ui/badge';
import { Skeleton } from '@/src/components/ui/skeleton';
import { Alert, AlertDescription } from '@/src/components/ui/alert';
import { Search, Filter, SortAsc, SortDesc, RefreshCw, AlertCircle } from 'lucide-react';
import { cn } from '@/src/lib/utils';

/**
 * Sort configuration
 */
export interface SortOption {
  key: string;
  label: string;
  direction?: 'asc' | 'desc';
}

/**
 * Filter configuration
 */
export interface FilterOption {
  key: string;
  label: string;
  value: string;
  count?: number;
}

/**
 * Column configuration for table-like display
 */
export interface ColumnConfig<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
  width?: string;
}

/**
 * Data list props
 */
export interface DataListProps<T> {
  // Data
  data: T[];
  loading?: boolean;
  error?: string;
  
  // Display
  renderItem: (item: T, index: number) => React.ReactNode;
  keyExtractor: (item: T) => string;
  emptyMessage?: string;
  emptyIcon?: React.ComponentType<{ className?: string }>;
  
  // Search and filtering
  searchable?: boolean;
  searchPlaceholder?: string;
  searchKeys?: (keyof T)[];
  filters?: FilterOption[];
  activeFilters?: string[];
  onFilterChange?: (filters: string[]) => void;
  
  // Sorting
  sortOptions?: SortOption[];
  defaultSort?: string;
  onSortChange?: (sortKey: string, direction: 'asc' | 'desc') => void;
  
  // Pagination
  pagination?: {
    page: number;
    limit: number;
    total: number;
    onPageChange: (page: number) => void;
    onLimitChange?: (limit: number) => void;
  };
  
  // Actions
  onRefresh?: () => void;
  bulkActions?: Array<{
    label: string;
    onClick: (selectedItems: T[]) => void;
    variant?: 'default' | 'destructive';
  }>;
  
  // Layout
  layout?: 'list' | 'grid' | 'table';
  gridCols?: 1 | 2 | 3 | 4 | 6;
  className?: string;
  
  // Accessibility
  'aria-label'?: string;
  'data-testid'?: string;
}

/**
 * Loading skeleton for data list
 */
function DataListSkeleton({ 
  layout = 'list', 
  gridCols = 1,
  count = 6 
}: { 
  layout?: 'list' | 'grid' | 'table';
  gridCols?: number;
  count?: number;
}) {
  const skeletonItems = Array.from({ length: count }, (_, i) => (
    <div key={i} className="space-y-3">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-3 w-1/2" />
      <Skeleton className="h-8 w-20" />
    </div>
  ));

  if (layout === 'grid') {
    return (
      <div className={cn(
        'grid gap-4',
        gridCols === 1 && 'grid-cols-1',
        gridCols === 2 && 'grid-cols-1 md:grid-cols-2',
        gridCols === 3 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        gridCols === 4 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
        gridCols === 6 && 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6'
      )}>
        {skeletonItems}
      </div>
    );
  }

  return <div className="space-y-4">{skeletonItems}</div>;
}

/**
 * Empty state component
 */
function EmptyState({ 
  message = 'No items found',
  icon: Icon = AlertCircle,
  onRefresh
}: {
  message?: string;
  icon?: React.ComponentType<{ className?: string }>;
  onRefresh?: () => void;
}) {
  return (
    <div className="text-center py-12">
      <Icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <p className="text-muted-foreground mb-4">{message}</p>
      {onRefresh && (
        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      )}
    </div>
  );
}

/**
 * Search and filter controls
 */
function ListControls<T>({
  searchable,
  searchPlaceholder = 'Search...',
  searchValue,
  onSearchChange,
  filters = [],
  activeFilters = [],
  onFilterChange,
  sortOptions = [],
  currentSort,
  onSortChange,
  onRefresh
}: {
  searchable?: boolean;
  searchPlaceholder?: string;
  searchValue: string;
  onSearchChange: (value: string) => void;
  filters?: FilterOption[];
  activeFilters?: string[];
  onFilterChange?: (filters: string[]) => void;
  sortOptions?: SortOption[];
  currentSort?: { key: string; direction: 'asc' | 'desc' };
  onSortChange?: (sortKey: string, direction: 'asc' | 'desc') => void;
  onRefresh?: () => void;
}) {
  const handleFilterToggle = (filterValue: string) => {
    if (!onFilterChange) return;
    
    const newFilters = activeFilters.includes(filterValue)
      ? activeFilters.filter(f => f !== filterValue)
      : [...activeFilters, filterValue];
    
    onFilterChange(newFilters);
  };

  return (
    <div className="space-y-4 mb-6">
      {/* Search and actions row */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        {searchable && (
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
        )}
        
        <div className="flex items-center gap-2">
          {/* Sort dropdown */}
          {sortOptions.length > 0 && (
            <Select
              value={currentSort ? `${currentSort.key}-${currentSort.direction}` : ''}
              onValueChange={(value) => {
                const [key, direction] = value.split('-') as [string, 'asc' | 'desc'];
                onSortChange?.(key, direction);
              }}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Sort by..." />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem 
                    key={`${option.key}-${option.direction || 'asc'}`}
                    value={`${option.key}-${option.direction || 'asc'}`}
                  >
                    <div className="flex items-center">
                      {option.direction === 'desc' ? (
                        <SortDesc className="h-4 w-4 mr-2" />
                      ) : (
                        <SortAsc className="h-4 w-4 mr-2" />
                      )}
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          
          {/* Refresh button */}
          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      {filters.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filters:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <Badge
                key={filter.value}
                variant={activeFilters.includes(filter.value) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => handleFilterToggle(filter.value)}
              >
                {filter.label}
                {filter.count !== undefined && ` (${filter.count})`}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Pagination controls
 */
function PaginationControls({
  page,
  limit,
  total,
  onPageChange,
  onLimitChange
}: {
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
}) {
  const totalPages = Math.ceil(total / limit);
  const startItem = (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, total);

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6">
      <div className="text-sm text-muted-foreground">
        Showing {startItem}-{endItem} of {total} items
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(page - 1)}
          disabled={page <= 1}
        >
          Previous
        </Button>
        
        <div className="flex items-center gap-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const pageNum = i + 1;
            return (
              <Button
                key={pageNum}
                variant={pageNum === page ? 'default' : 'outline'}
                size="sm"
                onClick={() => onPageChange(pageNum)}
              >
                {pageNum}
              </Button>
            );
          })}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(page + 1)}
          disabled={page >= totalPages}
        >
          Next
        </Button>
      </div>
    </div>
  );
}

/**
 * Main data list component
 */
export function DataList<T>({
  data,
  loading = false,
  error,
  renderItem,
  keyExtractor,
  emptyMessage = 'No items found',
  emptyIcon,
  searchable = false,
  searchPlaceholder,
  searchKeys = [],
  filters = [],
  activeFilters = [],
  onFilterChange,
  sortOptions = [],
  defaultSort,
  onSortChange,
  pagination,
  onRefresh,
  layout = 'list',
  gridCols = 3,
  className,
  'aria-label': ariaLabel,
  'data-testid': testId,
}: DataListProps<T>) {
  const [searchValue, setSearchValue] = useState('');
  const [currentSort, setCurrentSort] = useState<{ key: string; direction: 'asc' | 'desc' } | undefined>(
    defaultSort ? { key: defaultSort, direction: 'asc' } : undefined
  );

  // Filter and search data
  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply search
    if (searchable && searchValue && searchKeys.length > 0) {
      const searchLower = searchValue.toLowerCase();
      result = result.filter(item =>
        searchKeys.some(key => {
          const value = item[key];
          return String(value).toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply filters
    if (activeFilters.length > 0) {
      // This is a simplified filter implementation
      // In practice, you'd implement more sophisticated filtering logic
      result = result.filter(item => {
        // Custom filter logic would go here
        return true;
      });
    }

    return result;
  }, [data, searchValue, searchKeys, activeFilters, searchable]);

  const handleSortChange = (sortKey: string, direction: 'asc' | 'desc') => {
    setCurrentSort({ key: sortKey, direction });
    onSortChange?.(sortKey, direction);
  };

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={cn('space-y-4', className)} aria-label={ariaLabel} data-testid={testId}>
      {/* Controls */}
      <ListControls
        searchable={searchable}
        searchPlaceholder={searchPlaceholder}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        filters={filters}
        activeFilters={activeFilters}
        onFilterChange={onFilterChange}
        sortOptions={sortOptions}
        currentSort={currentSort}
        onSortChange={handleSortChange}
        onRefresh={onRefresh}
      />

      {/* Loading state */}
      {loading && (
        <DataListSkeleton layout={layout} gridCols={gridCols} />
      )}

      {/* Empty state */}
      {!loading && filteredData.length === 0 && (
        <EmptyState 
          message={emptyMessage}
          icon={emptyIcon}
          onRefresh={onRefresh}
        />
      )}

      {/* Data display */}
      {!loading && filteredData.length > 0 && (
        <>
          {layout === 'grid' ? (
            <div className={cn(
              'grid gap-4',
              gridCols === 1 && 'grid-cols-1',
              gridCols === 2 && 'grid-cols-1 md:grid-cols-2',
              gridCols === 3 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
              gridCols === 4 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
              gridCols === 6 && 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6'
            )}>
              {filteredData.map((item, index) => (
                <div key={keyExtractor(item)}>
                  {renderItem(item, index)}
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredData.map((item, index) => (
                <div key={keyExtractor(item)}>
                  {renderItem(item, index)}
                </div>
              ))}
            </div>
          )}
        </>
      )}

      {/* Pagination */}
      {pagination && !loading && filteredData.length > 0 && (
        <PaginationControls {...pagination} />
      )}
    </div>
  );
}
