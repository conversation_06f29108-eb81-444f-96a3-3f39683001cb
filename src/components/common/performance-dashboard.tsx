/**
 * Performance Dashboard Component
 * Displays real-time performance metrics and statistics
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Button } from '@/src/components/ui/button';
import { Badge } from '@/src/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/src/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/src/components/ui/tabs';
import { Alert, AlertDescription } from '@/src/components/ui/alert';
import { 
  Activity, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  RefreshCw,
  BarChart3,
  Zap
} from 'lucide-react';
import { usePerformanceStats, usePerformanceConfig, performanceMonitor } from '@/src/hooks/common/use-performance';
import { MetricType, PerformanceStats } from '@/src/lib/services/performance/monitor';
import { cn } from '@/src/lib/utils';

/**
 * Performance dashboard props
 */
export interface PerformanceDashboardProps {
  className?: string;
  refreshInterval?: number;
  showControls?: boolean;
  compactMode?: boolean;
}

/**
 * Metric card component
 */
function MetricCard({
  title,
  value,
  unit,
  trend,
  severity,
  icon: Icon,
  description
}: {
  title: string;
  value: number;
  unit: string;
  trend?: number;
  severity?: 'normal' | 'warning' | 'critical';
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}) {
  const formatValue = (val: number) => {
    if (unit === 'ms') {
      return val < 1000 ? `${val.toFixed(0)}ms` : `${(val / 1000).toFixed(1)}s`;
    }
    if (unit === 'ops/s') {
      return val.toFixed(1);
    }
    return val.toString();
  };

  const getSeverityColor = () => {
    switch (severity) {
      case 'critical': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-green-600';
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
        {trend !== undefined && (
          <div className="flex items-center mt-2">
            {trend > 0 ? (
              <TrendingUp className="h-3 w-3 text-red-500 mr-1" />
            ) : (
              <TrendingDown className="h-3 w-3 text-green-500 mr-1" />
            )}
            <span className={cn('text-xs', trend > 0 ? 'text-red-500' : 'text-green-500')}>
              {Math.abs(trend).toFixed(1)}% from last period
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Performance overview component
 */
function PerformanceOverview({ stats }: { stats: PerformanceStats }) {
  const getSeverity = (value: number, type: 'duration' | 'operations') => {
    if (type === 'duration') {
      if (value > 5000) return 'critical';
      if (value > 1000) return 'warning';
      return 'normal';
    }
    
    if (type === 'operations') {
      if (value > 10) return 'critical';
      if (value > 5) return 'warning';
      return 'normal';
    }
    
    return 'normal';
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <MetricCard
        title="Average Response"
        value={stats.averageDuration}
        unit="ms"
        severity={getSeverity(stats.averageDuration, 'duration')}
        icon={Clock}
        description="Average operation duration"
      />
      
      <MetricCard
        title="95th Percentile"
        value={stats.p95Duration}
        unit="ms"
        severity={getSeverity(stats.p95Duration, 'duration')}
        icon={TrendingUp}
        description="95% of operations complete within"
      />
      
      <MetricCard
        title="Operations/sec"
        value={stats.operationsPerSecond}
        unit="ops/s"
        icon={Activity}
        description="Operations per second"
      />
      
      <MetricCard
        title="Slow Operations"
        value={stats.slowOperations + stats.criticalOperations}
        unit=""
        severity={getSeverity(stats.slowOperations + stats.criticalOperations, 'operations')}
        icon={AlertTriangle}
        description="Operations exceeding thresholds"
      />
    </div>
  );
}

/**
 * Recent metrics table
 */
function RecentMetrics({ type }: { type?: MetricType }) {
  const { getMetrics } = usePerformanceConfig();
  const [metrics] = useState(() => getMetrics(type, 20));

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-4 gap-4 text-sm font-medium text-muted-foreground border-b pb-2">
        <div>Operation</div>
        <div>Duration</div>
        <div>Type</div>
        <div>Status</div>
      </div>
      
      <div className="space-y-1 max-h-64 overflow-y-auto">
        {metrics.map((metric) => (
          <div key={metric.id} className="grid grid-cols-4 gap-4 text-sm py-2 border-b border-border/50">
            <div className="truncate">{metric.name}</div>
            <div className="font-mono">
              {metric.duration < 1000 
                ? `${metric.duration.toFixed(0)}ms`
                : `${(metric.duration / 1000).toFixed(1)}s`
              }
            </div>
            <div>
              <Badge variant="outline" className="text-xs">
                {metric.type}
              </Badge>
            </div>
            <div>
              <Badge 
                variant={
                  metric.severity === 'critical' ? 'destructive' :
                  metric.severity === 'slow' ? 'secondary' : 'default'
                }
                className="text-xs"
              >
                {metric.severity}
              </Badge>
            </div>
          </div>
        ))}
      </div>
      
      {metrics.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          No recent metrics available
        </div>
      )}
    </div>
  );
}

/**
 * Performance controls
 */
function PerformanceControls() {
  const { updateConfig, clearMetrics } = usePerformanceConfig();
  const [sampling, setSampling] = useState('1.0');

  const handleSamplingChange = (value: string) => {
    setSampling(value);
    updateConfig({
      sampling: {
        rate: parseFloat(value),
        alwaysTrackSlow: true,
        alwaysTrackCritical: true,
      }
    });
  };

  return (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium">Sampling Rate:</label>
        <Select value={sampling} onValueChange={handleSamplingChange}>
          <SelectTrigger className="w-24">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="0.1">10%</SelectItem>
            <SelectItem value="0.5">50%</SelectItem>
            <SelectItem value="1.0">100%</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <Button
        variant="outline"
        size="sm"
        onClick={clearMetrics}
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Clear Metrics
      </Button>
    </div>
  );
}

/**
 * Main performance dashboard component
 */
export function PerformanceDashboard({
  className,
  refreshInterval = 5000,
  showControls = true,
  compactMode = false
}: PerformanceDashboardProps) {
  const [timeRange, setTimeRange] = useState(60);
  const [selectedType, setSelectedType] = useState<MetricType | undefined>();
  
  const stats = usePerformanceStats(timeRange, selectedType, refreshInterval);

  const metricTypes: MetricType[] = [
    'api_request',
    'database_query',
    'component_render',
    'page_load',
    'user_interaction',
    'webhook_processing',
    'background_task',
    'custom'
  ];

  if (compactMode) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.averageDuration.toFixed(0)}ms</div>
              <div className="text-xs text-muted-foreground">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.slowOperations + stats.criticalOperations}</div>
              <div className="text-xs text-muted-foreground">Slow Ops</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Zap className="h-6 w-6" />
            Performance Dashboard
          </h2>
          <p className="text-muted-foreground">
            Real-time application performance monitoring
          </p>
        </div>
        
        {showControls && <PerformanceControls />}
      </div>

      {/* Time range and type filters */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Time Range:</label>
          <Select value={timeRange.toString()} onValueChange={(value) => setTimeRange(parseInt(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="15">15 minutes</SelectItem>
              <SelectItem value="60">1 hour</SelectItem>
              <SelectItem value="240">4 hours</SelectItem>
              <SelectItem value="1440">24 hours</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Metric Type:</label>
          <Select value={selectedType || 'all'} onValueChange={(value) => setSelectedType(value === 'all' ? undefined : value as MetricType)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {metricTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type.replace('_', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Performance overview */}
      <PerformanceOverview stats={stats} />

      {/* Detailed metrics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="recent">Recent Metrics</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>
                  Last {timeRange} minutes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Operations:</span>
                    <span className="font-mono">{stats.totalMetrics}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Median Duration:</span>
                    <span className="font-mono">{stats.medianDuration.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>99th Percentile:</span>
                    <span className="font-mono">{stats.p99Duration.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Operations/sec:</span>
                    <span className="font-mono">{stats.operationsPerSecond.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Performance Issues</CardTitle>
                <CardDescription>
                  Operations exceeding thresholds
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Slow Operations:</span>
                    <Badge variant="secondary">{stats.slowOperations}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Critical Operations:</span>
                    <Badge variant="destructive">{stats.criticalOperations}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Success Rate:</span>
                    <span className="font-mono">
                      {((stats.totalMetrics - stats.criticalOperations) / Math.max(stats.totalMetrics, 1) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="recent">
          <Card>
            <CardHeader>
              <CardTitle>Recent Metrics</CardTitle>
              <CardDescription>
                Last 20 operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RecentMetrics type={selectedType} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Performance Alerts</CardTitle>
              <CardDescription>
                Current performance issues
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats.criticalOperations > 0 && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {stats.criticalOperations} operations exceeded critical thresholds in the last {timeRange} minutes.
                  </AlertDescription>
                </Alert>
              )}
              
              {stats.slowOperations > 5 && (
                <Alert className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {stats.slowOperations} operations exceeded slow thresholds. Consider investigating performance bottlenecks.
                  </AlertDescription>
                </Alert>
              )}
              
              {stats.criticalOperations === 0 && stats.slowOperations <= 5 && (
                <div className="text-center py-8 text-muted-foreground">
                  No performance alerts at this time
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
