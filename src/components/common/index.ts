/**
 * Common Components Index
 * Exports all standardized, reusable components
 */

// Data display components
export {
  DataCard,
  RepositoryCard,
  MetricCard,
  DetailCard,
  type DataCardProps,
  type CardAction,
  type CardBadge,
  type CardMetric
} from './data-card';

export {
  DataList,
  type DataListProps,
  type SortOption,
  type FilterOption,
  type ColumnConfig
} from './data-list';

// Form components
export {
  FormBuilder,
  type FormBuilderProps,
  type FieldConfig,
  type FormSection,
  type FieldType
} from './form-builder';

// Re-export commonly used UI components for convenience
export { Button } from '@/src/components/ui/button';
export { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
export { Input } from '@/src/components/ui/input';
export { Label } from '@/src/components/ui/label';
export { Badge } from '@/src/components/ui/badge';
export { Alert, AlertDescription, AlertTitle } from '@/src/components/ui/alert';
export { Skeleton } from '@/src/components/ui/skeleton';
export { Separator } from '@/src/components/ui/separator';

// Common patterns and utilities
export { cn } from '@/src/lib/utils';
