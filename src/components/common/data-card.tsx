/**
 * Standardized Data Card Component
 * Reusable card component for displaying data with consistent styling and behavior
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Button } from '@/src/components/ui/button';
import { Badge } from '@/src/components/ui/badge';
import { Skeleton } from '@/src/components/ui/skeleton';
import { cn } from '@/src/lib/utils';
import { LucideIcon } from 'lucide-react';

/**
 * Action button configuration
 */
export interface CardAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  icon?: LucideIcon;
  disabled?: boolean;
  loading?: boolean;
}

/**
 * Badge configuration
 */
export interface CardBadge {
  label: string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  color?: string;
}

/**
 * Metric display configuration
 */
export interface CardMetric {
  label: string;
  value: string | number;
  icon?: LucideIcon;
  color?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

/**
 * Data card props
 */
export interface DataCardProps {
  // Content
  title: string;
  description?: string;
  content?: React.ReactNode;
  
  // Visual elements
  icon?: LucideIcon;
  iconColor?: string;
  badges?: CardBadge[];
  metrics?: CardMetric[];
  
  // Actions
  actions?: CardAction[];
  onClick?: () => void;
  
  // State
  loading?: boolean;
  error?: string;
  
  // Styling
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  size?: 'sm' | 'md' | 'lg';
  
  // Accessibility
  'aria-label'?: string;
  'data-testid'?: string;
}

/**
 * Loading skeleton for data card
 */
function DataCardSkeleton({ variant = 'default', size = 'md' }: Pick<DataCardProps, 'variant' | 'size'>) {
  const isCompact = variant === 'compact';
  const height = size === 'sm' ? 'h-20' : size === 'lg' ? 'h-40' : 'h-32';
  
  return (
    <Card className={cn('animate-pulse', height)}>
      <CardHeader className={cn('pb-2', isCompact && 'pb-1')}>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            {!isCompact && <Skeleton className="h-3 w-32" />}
          </div>
          <Skeleton className="h-4 w-4 rounded" />
        </div>
      </CardHeader>
      <CardContent className={cn('pt-0', isCompact && 'py-2')}>
        <div className="space-y-2">
          <Skeleton className="h-6 w-16" />
          {!isCompact && <Skeleton className="h-3 w-20" />}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Error state for data card
 */
function DataCardError({ error, onRetry }: { error: string; onRetry?: () => void }) {
  return (
    <Card className="border-destructive/50 bg-destructive/5">
      <CardContent className="pt-6">
        <div className="text-center space-y-2">
          <p className="text-sm text-destructive">{error}</p>
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry}>
              Retry
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Metric display component
 */
function MetricDisplay({ metric }: { metric: CardMetric }) {
  const Icon = metric.icon;
  
  return (
    <div className="flex items-center space-x-2">
      {Icon && (
        <Icon 
          className={cn('h-4 w-4', metric.color || 'text-muted-foreground')} 
        />
      )}
      <div className="flex-1 min-w-0">
        <div className="text-lg font-semibold">{metric.value}</div>
        <div className="text-xs text-muted-foreground">{metric.label}</div>
      </div>
      {metric.trend && (
        <div className={cn(
          'text-xs font-medium',
          metric.trend.isPositive ? 'text-green-600' : 'text-red-600'
        )}>
          {metric.trend.isPositive ? '+' : ''}{metric.trend.value}%
        </div>
      )}
    </div>
  );
}

/**
 * Main data card component
 */
export function DataCard({
  title,
  description,
  content,
  icon: Icon,
  iconColor,
  badges = [],
  metrics = [],
  actions = [],
  onClick,
  loading = false,
  error,
  className,
  variant = 'default',
  size = 'md',
  'aria-label': ariaLabel,
  'data-testid': testId,
}: DataCardProps) {
  // Show loading state
  if (loading) {
    return <DataCardSkeleton variant={variant} size={size} />;
  }

  // Show error state
  if (error) {
    return <DataCardError error={error} onRetry={onClick} />;
  }

  const isCompact = variant === 'compact';
  const isDetailed = variant === 'detailed';
  const isClickable = !!onClick;

  const cardClasses = cn(
    'transition-all duration-200',
    isClickable && 'cursor-pointer hover:shadow-md hover:border-primary/30',
    size === 'sm' && 'text-sm',
    size === 'lg' && 'text-base',
    className
  );

  return (
    <Card 
      className={cardClasses}
      onClick={onClick}
      aria-label={ariaLabel}
      data-testid={testId}
    >
      <CardHeader className={cn('pb-2', isCompact && 'pb-1')}>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              {Icon && (
                <Icon 
                  className={cn(
                    'h-4 w-4 flex-shrink-0',
                    iconColor || 'text-primary'
                  )} 
                />
              )}
              <CardTitle className={cn(
                'truncate',
                isCompact ? 'text-sm' : 'text-base'
              )}>
                {title}
              </CardTitle>
            </div>
            {description && !isCompact && (
              <CardDescription className="mt-1 text-xs">
                {description}
              </CardDescription>
            )}
          </div>
          
          {/* Badges */}
          {badges.length > 0 && (
            <div className="flex flex-wrap gap-1 ml-2">
              {badges.map((badge, index) => (
                <Badge 
                  key={index}
                  variant={badge.variant}
                  className={cn('text-xs', badge.color)}
                >
                  {badge.label}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className={cn('pt-0', isCompact && 'py-2')}>
        {/* Metrics */}
        {metrics.length > 0 && (
          <div className={cn(
            'space-y-3',
            isCompact && 'space-y-2',
            metrics.length > 1 && 'grid grid-cols-1 gap-3',
            metrics.length > 2 && isDetailed && 'md:grid-cols-2'
          )}>
            {metrics.map((metric, index) => (
              <MetricDisplay key={index} metric={metric} />
            ))}
          </div>
        )}

        {/* Custom content */}
        {content && (
          <div className={cn('mt-3', isCompact && 'mt-2')}>
            {content}
          </div>
        )}

        {/* Actions */}
        {actions.length > 0 && (
          <div className={cn(
            'flex flex-wrap gap-2 mt-4',
            isCompact && 'mt-2',
            actions.length > 2 && 'justify-start',
            actions.length <= 2 && 'justify-end'
          )}>
            {actions.map((action, index) => {
              const ActionIcon = action.icon;
              return (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size={action.size || 'sm'}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick();
                  }}
                  disabled={action.disabled || action.loading}
                  className="flex-shrink-0"
                >
                  {action.loading ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  ) : ActionIcon ? (
                    <ActionIcon className="h-4 w-4 mr-1" />
                  ) : null}
                  {action.label}
                </Button>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Specialized data card variants
 */

// Repository card
export function RepositoryCard(props: Omit<DataCardProps, 'variant'>) {
  return <DataCard {...props} variant="default" />;
}

// Metric card
export function MetricCard(props: Omit<DataCardProps, 'variant'>) {
  return <DataCard {...props} variant="compact" />;
}

// Detail card
export function DetailCard(props: Omit<DataCardProps, 'variant'>) {
  return <DataCard {...props} variant="detailed" />;
}
