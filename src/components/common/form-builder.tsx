/**
 * Standardized Form Builder Component
 * Reusable form component with validation, field types, and consistent styling
 */

import React from 'react';
import { useForm, Controller, FieldValues, Path, PathValue } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import { Textarea } from '@/src/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/src/components/ui/select';
import { Checkbox } from '@/src/components/ui/checkbox';
import { Switch } from '@/src/components/ui/switch';
import { Label } from '@/src/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Alert, AlertDescription } from '@/src/components/ui/alert';
import { Separator } from '@/src/components/ui/separator';
import { cn } from '@/src/lib/utils';
import { LucideIcon, AlertCircle, Loader2 } from 'lucide-react';

/**
 * Field types
 */
export type FieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'textarea' 
  | 'select' 
  | 'checkbox' 
  | 'switch' 
  | 'radio'
  | 'file'
  | 'date'
  | 'custom';

/**
 * Field configuration
 */
export interface FieldConfig<T extends FieldValues> {
  name: Path<T>;
  type: FieldType;
  label: string;
  placeholder?: string;
  description?: string;
  required?: boolean;
  disabled?: boolean;
  
  // Field-specific options
  options?: Array<{ value: string; label: string; disabled?: boolean }>;
  multiple?: boolean;
  accept?: string; // for file inputs
  min?: number;
  max?: number;
  step?: number;
  
  // Custom rendering
  render?: (field: any, fieldState: any) => React.ReactNode;
  
  // Styling
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  
  // Conditional display
  condition?: (values: T) => boolean;
}

/**
 * Form section configuration
 */
export interface FormSection<T extends FieldValues> {
  title?: string;
  description?: string;
  icon?: LucideIcon;
  fields: FieldConfig<T>[];
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

/**
 * Form builder props
 */
export interface FormBuilderProps<T extends FieldValues> {
  // Form configuration
  schema: z.ZodSchema<T>;
  sections: FormSection<T>[];
  defaultValues?: Partial<T>;
  
  // Form behavior
  onSubmit: (data: T) => Promise<void> | void;
  onCancel?: () => void;
  onChange?: (data: Partial<T>) => void;
  
  // Form state
  loading?: boolean;
  disabled?: boolean;
  
  // Styling
  title?: string;
  description?: string;
  submitLabel?: string;
  cancelLabel?: string;
  layout?: 'single' | 'two-column' | 'sections';
  className?: string;
  
  // Accessibility
  'aria-label'?: string;
  'data-testid'?: string;
}

/**
 * Field renderer component
 */
function FieldRenderer<T extends FieldValues>({
  field,
  fieldConfig,
  error,
  disabled
}: {
  field: any;
  fieldConfig: FieldConfig<T>;
  error?: string;
  disabled?: boolean;
}) {
  const { type, placeholder, options, multiple, accept, min, max, step, size = 'md' } = fieldConfig;
  const isDisabled = disabled || fieldConfig.disabled;

  const inputClasses = cn(
    size === 'sm' && 'h-8 text-sm',
    size === 'lg' && 'h-12 text-lg',
    fieldConfig.className
  );

  switch (type) {
    case 'text':
    case 'email':
    case 'password':
      return (
        <Input
          {...field}
          type={type}
          placeholder={placeholder}
          disabled={isDisabled}
          className={inputClasses}
        />
      );

    case 'number':
      return (
        <Input
          {...field}
          type="number"
          placeholder={placeholder}
          min={min}
          max={max}
          step={step}
          disabled={isDisabled}
          className={inputClasses}
          onChange={(e) => {
            const value = e.target.value === '' ? undefined : e.target.valueAsNumber;
            field.onChange(value);
          }}
        />
      );

    case 'textarea':
      return (
        <Textarea
          {...field}
          placeholder={placeholder}
          disabled={isDisabled}
          className={cn('min-h-[80px]', inputClasses)}
        />
      );

    case 'select':
      return (
        <Select
          value={field.value}
          onValueChange={field.onChange}
          disabled={isDisabled}
        >
          <SelectTrigger className={inputClasses}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {options?.map((option) => (
              <SelectItem 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case 'checkbox':
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={field.value}
            onCheckedChange={field.onChange}
            disabled={isDisabled}
          />
          <Label className="text-sm font-normal">
            {fieldConfig.label}
          </Label>
        </div>
      );

    case 'switch':
      return (
        <div className="flex items-center justify-between">
          <Label className="text-sm font-normal">
            {fieldConfig.label}
          </Label>
          <Switch
            checked={field.value}
            onCheckedChange={field.onChange}
            disabled={isDisabled}
          />
        </div>
      );

    case 'file':
      return (
        <Input
          type="file"
          accept={accept}
          multiple={multiple}
          disabled={isDisabled}
          className={inputClasses}
          onChange={(e) => {
            const files = e.target.files;
            field.onChange(multiple ? files : files?.[0]);
          }}
        />
      );

    case 'date':
      return (
        <Input
          {...field}
          type="date"
          disabled={isDisabled}
          className={inputClasses}
        />
      );

    case 'custom':
      return fieldConfig.render?.(field, { error }) || null;

    default:
      return (
        <Input
          {...field}
          placeholder={placeholder}
          disabled={isDisabled}
          className={inputClasses}
        />
      );
  }
}

/**
 * Form field component
 */
function FormField<T extends FieldValues>({
  fieldConfig,
  control,
  disabled,
  values
}: {
  fieldConfig: FieldConfig<T>;
  control: any;
  disabled?: boolean;
  values: T;
}) {
  // Check if field should be displayed
  if (fieldConfig.condition && !fieldConfig.condition(values)) {
    return null;
  }

  const isCheckboxOrSwitch = fieldConfig.type === 'checkbox' || fieldConfig.type === 'switch';

  return (
    <div className="space-y-2">
      {!isCheckboxOrSwitch && (
        <Label htmlFor={fieldConfig.name} className="text-sm font-medium">
          {fieldConfig.label}
          {fieldConfig.required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}
      
      <Controller
        name={fieldConfig.name}
        control={control}
        render={({ field, fieldState }) => (
          <>
            <FieldRenderer
              field={field}
              fieldConfig={fieldConfig}
              error={fieldState.error?.message}
              disabled={disabled}
            />
            {fieldState.error && (
              <p className="text-sm text-destructive flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {fieldState.error.message}
              </p>
            )}
          </>
        )}
      />
      
      {fieldConfig.description && (
        <p className="text-xs text-muted-foreground">
          {fieldConfig.description}
        </p>
      )}
    </div>
  );
}

/**
 * Form section component
 */
function FormSectionComponent<T extends FieldValues>({
  section,
  control,
  disabled,
  values
}: {
  section: FormSection<T>;
  control: any;
  disabled?: boolean;
  values: T;
}) {
  const [collapsed, setCollapsed] = React.useState(section.defaultCollapsed || false);
  const Icon = section.icon;

  const visibleFields = section.fields.filter(field => 
    !field.condition || field.condition(values)
  );

  if (visibleFields.length === 0) {
    return null;
  }

  const content = (
    <div className="grid gap-4 md:grid-cols-2">
      {visibleFields.map((field) => (
        <div key={field.name} className={field.type === 'textarea' ? 'md:col-span-2' : ''}>
          <FormField
            fieldConfig={field}
            control={control}
            disabled={disabled}
            values={values}
          />
        </div>
      ))}
    </div>
  );

  if (!section.title) {
    return content;
  }

  return (
    <Card>
      <CardHeader 
        className={cn(
          section.collapsible && 'cursor-pointer',
          'pb-4'
        )}
        onClick={section.collapsible ? () => setCollapsed(!collapsed) : undefined}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {Icon && <Icon className="h-5 w-5 text-primary" />}
            <CardTitle className="text-lg">{section.title}</CardTitle>
          </div>
          {section.collapsible && (
            <Button variant="ghost" size="sm">
              {collapsed ? 'Expand' : 'Collapse'}
            </Button>
          )}
        </div>
        {section.description && (
          <CardDescription>{section.description}</CardDescription>
        )}
      </CardHeader>
      {!collapsed && (
        <CardContent className="pt-0">
          {content}
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Main form builder component
 */
export function FormBuilder<T extends FieldValues>({
  schema,
  sections,
  defaultValues,
  onSubmit,
  onCancel,
  onChange,
  loading = false,
  disabled = false,
  title,
  description,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  layout = 'sections',
  className,
  'aria-label': ariaLabel,
  'data-testid': testId,
}: FormBuilderProps<T>) {
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<T>({
    resolver: zodResolver(schema as any),
    defaultValues: defaultValues as any,
    mode: 'onChange'
  });

  const values = watch();
  const isLoading = loading || isSubmitting;
  const isDisabled = disabled || isLoading;

  React.useEffect(() => {
    if (onChange && isDirty) {
      onChange(values);
    }
  }, [values, onChange, isDirty]);

  const handleFormSubmit = async (data: T) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <form 
      onSubmit={handleSubmit(handleFormSubmit)}
      className={cn('space-y-6', className)}
      aria-label={ariaLabel}
      data-testid={testId}
    >
      {/* Form header */}
      {(title || description) && (
        <div className="space-y-2">
          {title && <h2 className="text-2xl font-bold">{title}</h2>}
          {description && <p className="text-muted-foreground">{description}</p>}
          <Separator />
        </div>
      )}

      {/* Form sections */}
      <div className="space-y-6">
        {sections.map((section, index) => (
          <FormSectionComponent
            key={index}
            section={section}
            control={control}
            disabled={isDisabled}
            values={values}
          />
        ))}
      </div>

      {/* Form actions */}
      <div className="flex flex-col-reverse sm:flex-row gap-3 pt-6">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="sm:w-auto"
          >
            {cancelLabel}
          </Button>
        )}
        <Button
          type="submit"
          disabled={isDisabled}
          className="sm:w-auto sm:ml-auto"
        >
          {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {submitLabel}
        </Button>
      </div>

      {/* Global form errors */}
      {Object.keys(errors).length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please fix the errors above and try again.
          </AlertDescription>
        </Alert>
      )}
    </form>
  );
}
