import mongoose from 'mongoose';
import { env, database } from '@/src/lib/config/environment';

/**
 * Database connection states (matching Mongoose ConnectionStates)
 */
export enum ConnectionState {
  DISCONNECTED = 0,
  CONNECTED = 1,
  CONNECTING = 2,
  DISCONNECTING = 3,
}

// Type alias for Mongoose connection states
type MongooseConnectionState = typeof mongoose.connection.readyState;

/**
 * Database health status
 */
export interface DatabaseHealth {
  status: 'healthy' | 'unhealthy' | 'connecting';
  message: string;
  connectionState: ConnectionState;
  timestamp: Date;
  details?: {
    readyState: number;
    host?: string;
    name?: string;
    collections?: number;
  };
}

/**
 * Database connection options
 */
interface DatabaseConnectionOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
}

/**
 * Centralized Database Service
 * Provides unified database access with proper connection management,
 * health monitoring, and error handling
 */
export class DatabaseService {
  private static instance: DatabaseService;
  private connectionPromise: Promise<mongoose.Connection> | null = null;
  private isConnecting = false;
  private connectionAttempts = 0;
  private readonly maxRetries = 3;
  private readonly retryDelay = 2000; // 2 seconds

  private constructor() {
    this.setupConnectionEventHandlers();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Setup connection event handlers
   */
  private setupConnectionEventHandlers(): void {
    mongoose.connection.on('connected', () => {
      console.log('✅ Database connected successfully');
      this.connectionAttempts = 0;
    });

    mongoose.connection.on('error', (error) => {
      console.error('❌ Database connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️ Database disconnected');
      this.connectionPromise = null;
    });

    mongoose.connection.on('reconnected', () => {
      console.log('🔄 Database reconnected');
    });

    // Handle process termination
    process.on('SIGINT', () => this.disconnect());
    process.on('SIGTERM', () => this.disconnect());
  }

  /**
   * Connect to database with retry logic
   */
  public async connect(options: DatabaseConnectionOptions = {}): Promise<mongoose.Connection> {
    const { maxRetries = this.maxRetries, retryDelay = this.retryDelay, timeout = 30000 } = options;

    // Return existing connection if available
    if (mongoose.connection.readyState === 1) { // CONNECTED
      return mongoose.connection;
    }

    // Return existing connection promise if connecting
    if (this.connectionPromise && this.isConnecting) {
      return this.connectionPromise;
    }

    this.isConnecting = true;
    this.connectionPromise = this.attemptConnection(maxRetries, retryDelay, timeout);

    try {
      const connection = await this.connectionPromise;
      this.isConnecting = false;
      return connection;
    } catch (error) {
      this.isConnecting = false;
      this.connectionPromise = null;
      throw error;
    }
  }

  /**
   * Attempt database connection with retries
   */
  private async attemptConnection(
    maxRetries: number,
    retryDelay: number,
    timeout: number
  ): Promise<mongoose.Connection> {
    const connectionOptions: mongoose.ConnectOptions = {
      bufferCommands: false,
      maxPoolSize: database.connectionLimit,
      minPoolSize: 2,
      serverSelectionTimeoutMS: timeout,
      socketTimeoutMS: 45000,
      connectTimeoutMS: timeout,
      family: 4,
      retryWrites: true,
      w: 'majority' as const,
      maxIdleTimeMS: 30000,
      heartbeatFrequencyMS: 10000,
      waitQueueTimeoutMS: 30000,
    };

    while (this.connectionAttempts < maxRetries) {
      try {
        this.connectionAttempts++;
        console.log(`🔄 Attempting database connection (${this.connectionAttempts}/${maxRetries})...`);

        await mongoose.connect(env.MONGODB_URI, connectionOptions);
        return mongoose.connection;
      } catch (error) {
        console.error(`❌ Connection attempt ${this.connectionAttempts} failed:`, error);

        if (this.connectionAttempts >= maxRetries) {
          throw new Error(
            `Failed to connect to database after ${maxRetries} attempts: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }

        // Wait before retrying with exponential backoff
        const delay = retryDelay * Math.pow(2, this.connectionAttempts - 1);
        console.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new Error('Maximum connection attempts exceeded');
  }

  /**
   * Disconnect from database
   */
  public async disconnect(): Promise<void> {
    try {
      if (mongoose.connection.readyState !== 0) { // DISCONNECTED
        console.log('🔄 Disconnecting from database...');
        await mongoose.disconnect();
        console.log('✅ Database disconnected successfully');
      }
    } catch (error) {
      console.error('❌ Error disconnecting from database:', error);
      throw error;
    } finally {
      this.connectionPromise = null;
      this.isConnecting = false;
      this.connectionAttempts = 0;
    }
  }

  /**
   * Get database health status
   */
  public async getHealth(): Promise<DatabaseHealth> {
    const timestamp = new Date();
    const readyState = mongoose.connection.readyState;

    try {
      if (readyState === 1) { // CONNECTED
        // Test the connection with a simple operation
        if (mongoose.connection.db) {
          await mongoose.connection.db.admin().ping();
        }

        return {
          status: 'healthy',
          message: 'Database connection is active and responsive',
          connectionState: readyState as unknown as ConnectionState,
          timestamp,
          details: {
            readyState,
            host: mongoose.connection.host,
            name: mongoose.connection.name,
            collections: Object.keys(mongoose.connection.collections).length,
          },
        };
      } else if (readyState === 2) { // CONNECTING
        return {
          status: 'connecting',
          message: 'Database connection is being established',
          connectionState: readyState as unknown as ConnectionState,
          timestamp,
          details: { readyState },
        };
      } else {
        return {
          status: 'unhealthy',
          message: 'Database is not connected',
          connectionState: readyState as unknown as ConnectionState,
          timestamp,
          details: { readyState },
        };
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `Database health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        connectionState: readyState as unknown as ConnectionState,
        timestamp,
        details: { readyState },
      };
    }
  }

  /**
   * Ensure database connection is established
   * This is the main method that should be called before any database operations
   */
  public async ensureConnection(): Promise<mongoose.Connection> {
    try {
      return await this.connect();
    } catch (error) {
      console.error('❌ Failed to ensure database connection:', error);
      throw new Error(
        `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get current connection state
   */
  public getConnectionState(): ConnectionState {
    return mongoose.connection.readyState as unknown as ConnectionState;
  }

  /**
   * Check if database is connected
   */
  public isConnected(): boolean {
    return mongoose.connection.readyState === 1; // CONNECTED
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats() {
    const connection = mongoose.connection;
    return {
      readyState: connection.readyState,
      host: connection.host,
      port: connection.port,
      name: connection.name,
      collections: Object.keys(connection.collections).length,
      models: Object.keys(mongoose.models).length,
    };
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();

// Export convenience functions
export async function connectToDatabase(): Promise<mongoose.Connection> {
  return databaseService.ensureConnection();
}

export async function checkDatabaseHealth(): Promise<DatabaseHealth> {
  return databaseService.getHealth();
}

export function getDatabaseStats() {
  return databaseService.getConnectionStats();
}
