import { databaseService, checkDatabaseHealth as getHealthStatus } from './service';

/**
 * Initialize database connection and ensure models are loaded
 * This should be called early in the application lifecycle
 */
export async function initializeDatabase() {
  try {
    console.log('🔄 Initializing database connection...');

    // Connect to MongoDB via the centralized service
    await databaseService.ensureConnection();

    // Import models to ensure they are registered
    await import('./models');

    console.log('✅ Database initialized successfully');

    return true;
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

/**
 * Health check for database connection
 * @deprecated Use checkDatabaseHealth from service.ts instead
 */
export async function checkDatabaseHealth() {
  const health = await getHealthStatus();
  return {
    status: health.status === 'healthy' ? 'healthy' : 'unhealthy',
    message: health.message
  };
}
