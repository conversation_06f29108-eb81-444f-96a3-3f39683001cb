/**
 * Rate limiter for webhook protection
 * Implements a sliding window rate limiting algorithm
 */
export class RateLimiter {
  private requests = new Map<string, number[]>();

  /**
   * Check if a request is allowed based on rate limiting rules
   * @param key - Unique identifier for the rate limit bucket
   * @param maxRequests - Maximum number of requests allowed in the window
   * @param windowMs - Time window in milliseconds
   * @returns true if request is allowed, false if rate limited
   */
  isAllowed(key: string, maxRequests = 10, windowMs = 60000): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];

    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);

    if (validRequests.length >= maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(key, validRequests);
    return true;
  }

  /**
   * Clear all rate limit data for a specific key
   * @param key - The key to clear
   */
  clear(key: string): void {
    this.requests.delete(key);
  }

  /**
   * Clear all rate limit data
   */
  clearAll(): void {
    this.requests.clear();
  }

  /**
   * Get current request count for a key within the window
   * @param key - The key to check
   * @param windowMs - Time window in milliseconds
   * @returns Current number of requests in the window
   */
  getCurrentCount(key: string, windowMs = 60000): number {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    return requests.filter(time => now - time < windowMs).length;
  }
}
