/**
 * GitHub Webhook Types
 * Comprehensive type definitions for GitHub webhook payloads
 */

/**
 * Base webhook payload structure
 */
export interface BaseWebhookPayload {
  action: string;
  sender: GitHubUser;
  repository?: GitHubRepository;
  organization?: GitHubOrganization;
  installation?: GitHubInstallation;
}

/**
 * GitHub User
 */
export interface GitHubUser {
  id: number;
  login: string;
  avatar_url: string;
  type: 'User' | 'Bot' | 'Organization';
  site_admin: boolean;
  html_url: string;
}

/**
 * GitHub Organization
 */
export interface GitHubOrganization {
  id: number;
  login: string;
  avatar_url: string;
  description?: string;
  html_url: string;
  type: 'Organization';
}

/**
 * GitHub Repository
 */
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  private: boolean;
  description?: string;
  language?: string;
  stargazers_count: number;
  forks_count: number;
  default_branch: string;
  html_url: string;
  clone_url: string;
  ssh_url: string;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  owner: GitHubUser;
  permissions?: {
    admin: boolean;
    push: boolean;
    pull: boolean;
  };
}

/**
 * GitHub Installation
 */
export interface GitHubInstallation {
  id: number;
  account: GitHubUser | GitHubOrganization;
  repository_selection: 'all' | 'selected';
  access_tokens_url: string;
  repositories_url: string;
  html_url: string;
  app_id: number;
  target_id: number;
  target_type: 'Organization' | 'User';
  permissions: Record<string, string>;
  events: string[];
  created_at: string;
  updated_at: string;
  single_file_name?: string;
  has_multiple_single_files?: boolean;
  single_file_paths?: string[];
  app_slug: string;
  suspended_by?: GitHubUser;
  suspended_at?: string;
}

/**
 * GitHub Pull Request
 */
export interface GitHubPullRequest {
  id: number;
  number: number;
  title: string;
  body?: string;
  state: 'open' | 'closed' | 'draft';
  draft: boolean;
  mergeable?: boolean;
  mergeable_state: string;
  merged: boolean;
  merged_at?: string;
  merge_commit_sha?: string;
  user: GitHubUser;
  assignee?: GitHubUser;
  assignees: GitHubUser[];
  requested_reviewers: GitHubUser[];
  head: {
    ref: string;
    sha: string;
    repo: GitHubRepository;
  };
  base: {
    ref: string;
    sha: string;
    repo: GitHubRepository;
  };
  html_url: string;
  diff_url: string;
  patch_url: string;
  commits_url: string;
  review_comments_url: string;
  statuses_url: string;
  created_at: string;
  updated_at: string;
  closed_at?: string;
  commits: number;
  additions: number;
  deletions: number;
  changed_files: number;
}

/**
 * Installation webhook payload
 */
export interface InstallationWebhookPayload extends BaseWebhookPayload {
  action: 'created' | 'deleted' | 'suspend' | 'unsuspend' | 'new_permissions_accepted';
  installation: GitHubInstallation;
  repositories?: GitHubRepository[];
  repository_selection?: 'all' | 'selected';
}

/**
 * Installation repositories webhook payload
 */
export interface InstallationRepositoriesWebhookPayload extends BaseWebhookPayload {
  action: 'added' | 'removed';
  installation: GitHubInstallation;
  repository_selection: 'all' | 'selected';
  repositories_added?: GitHubRepository[];
  repositories_removed?: GitHubRepository[];
}

/**
 * Pull request webhook payload
 */
export interface PullRequestWebhookPayload extends BaseWebhookPayload {
  action: 'opened' | 'closed' | 'reopened' | 'synchronize' | 'edited' | 'assigned' | 'unassigned' | 'review_requested' | 'review_request_removed' | 'labeled' | 'unlabeled' | 'ready_for_review' | 'converted_to_draft';
  pull_request: GitHubPullRequest;
  repository: GitHubRepository;
  number: number;
  changes?: Record<string, unknown>;
  before?: string;
  after?: string;
}

/**
 * Repository webhook payload
 */
export interface RepositoryWebhookPayload extends BaseWebhookPayload {
  action: 'created' | 'deleted' | 'archived' | 'unarchived' | 'edited' | 'renamed' | 'transferred' | 'publicized' | 'privatized';
  repository: GitHubRepository;
  changes?: {
    name?: {
      from: string;
    };
    description?: {
      from: string;
    };
    default_branch?: {
      from: string;
    };
    visibility?: {
      from: 'public' | 'private' | 'internal';
    };
  };
}

/**
 * Push webhook payload
 */
export interface PushWebhookPayload extends BaseWebhookPayload {
  ref: string;
  before: string;
  after: string;
  created: boolean;
  deleted: boolean;
  forced: boolean;
  base_ref?: string;
  compare: string;
  commits: Array<{
    id: string;
    tree_id: string;
    distinct: boolean;
    message: string;
    timestamp: string;
    url: string;
    author: {
      name: string;
      email: string;
      username?: string;
    };
    committer: {
      name: string;
      email: string;
      username?: string;
    };
    added: string[];
    removed: string[];
    modified: string[];
  }>;
  head_commit?: {
    id: string;
    tree_id: string;
    distinct: boolean;
    message: string;
    timestamp: string;
    url: string;
    author: {
      name: string;
      email: string;
      username?: string;
    };
    committer: {
      name: string;
      email: string;
      username?: string;
    };
    added: string[];
    removed: string[];
    modified: string[];
  };
  repository: GitHubRepository;
  pusher: {
    name: string;
    email: string;
  };
}

/**
 * Webhook sync result
 */
export interface WebhookSyncResult {
  success: boolean;
  action: string;
  repositoriesAffected: number;
  organizationId?: string;
  errors: string[];
  metrics?: {
    duration: number;
    timestamp: Date;
  };
}

/**
 * Webhook headers
 */
export interface WebhookHeaders {
  'x-github-event': string;
  'x-github-delivery': string;
  'x-github-hook-id': string;
  'x-github-hook-installation-target-id': string;
  'x-github-hook-installation-target-type': string;
  'x-hub-signature-256': string;
  'user-agent': string;
}

/**
 * Processed webhook data for external API
 */
export interface ProcessedWebhookData {
  id: number;
  number: number;
  title: string;
  state: string;
  action: string;
  repository: string;
  author: string;
  sender: string;
  github_event: string;
  github_delivery: string;
  url?: string;
  base_branch?: string;
  head_branch?: string;
  draft?: boolean;
  mergeable?: boolean;
  created_at?: string;
  updated_at?: string;
  diff_url?: string;
  patch_url?: string;
  commits?: number;
  additions?: number;
  deletions?: number;
  changed_files?: number;
  commits_url?: string;
  review_comments_url?: string;
  statuses_url?: string;
}
