/**
 * Type definitions for security review payloads from Python backend
 */

export interface SecurityComment {
  body: string;
  file_path?: string;
  line?: number;
  severity?: 'critical' | 'high' | 'medium' | 'low' | string;
  comment_type: string;
  finding_ids?: string[];
}

export interface SecurityAnalysisSummary {
  total_findings: number;
  critical_findings: number;
  high_findings: number;
  success: boolean;
  analyzers_run: number;
}

export interface SecurityPRInfo {
  id: number;
  number: number;
  title: string;
  repository: string; // Format: "owner/repo"
  author: string;
  url: string;
  base_branch: string;
  head_branch: string;
  action: string;
}

export interface SecurityReviewPayload {
  pr_info: SecurityPRInfo;
  analysis_summary: SecurityAnalysisSummary;
  comments: SecurityComment[];
  timestamp: number; // Unix timestamp
}

export interface SecurityReviewResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string[];
  data?: {
    pr_number: number;
    repository: string;
    total_findings: number;
    comments_posted?: number;
    comments_attempted?: number;
    errors?: string[];
  };
}

/**
 * Severity levels for security findings
 */
export enum SecuritySeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

/**
 * Comment types for security findings
 */
export enum SecurityCommentType {
  VULNERABILITY = 'vulnerability',
  CODE_QUALITY = 'code_quality',
  BEST_PRACTICE = 'best_practice',
  SECURITY_HOTSPOT = 'security_hotspot',
  COMPLIANCE = 'compliance'
}

/**
 * Analysis status
 */
export enum AnalysisStatus {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL = 'partial'
}
