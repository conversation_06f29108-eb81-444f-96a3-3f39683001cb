/**
 * Common API Response Types
 * Standardized interfaces for all API responses
 */

/**
 * Base API response structure
 */
export interface BaseAPIResponse {
  timestamp: string;
}

/**
 * Success API response
 */
export interface SuccessAPIResponse<T = unknown> extends BaseAPIResponse {
  success: true;
  data: T;
  message?: string;
}

/**
 * Error API response
 */
export interface ErrorAPIResponse extends BaseAPIResponse {
  success: false;
  error: string;
  message: string;
  details?: Record<string, unknown>;
  code?: string;
}

/**
 * API response union type
 */
export type APIResponse<T = unknown> = SuccessAPIResponse<T> | ErrorAPIResponse;

/**
 * Paginated response
 */
export interface PaginatedResponse<T> extends BaseAPIResponse {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Organization API types
 */
export interface OrganizationResponse {
  id: string;
  name: string;
  provider: 'github' | 'gitlab';
  avatar?: string;
  isCurrent?: boolean;
  type: 'personal' | 'organization';
  description?: string;
  publicRepos?: number;
  path?: string;
  installationStatus?: 'active' | 'pending' | 'suspended' | 'deleted';
  repoCount?: number;
}

export interface OrganizationsAPIResponse extends BaseAPIResponse {
  organizations: OrganizationResponse[];
  totalCount: number;
  missingProviders: string[];
  errors?: Array<{
    provider: string;
    error: string;
  }>;
  user: {
    id: string;
    email?: string;
    name?: string;
  };
}

/**
 * Repository API types
 */
export interface RepositoryResponse {
  id: string;
  name: string;
  fullName: string;
  description?: string;
  private: boolean;
  language?: string;
  stars: number;
  forks: number;
  url?: string;
  defaultBranch?: string;
  lastSync: string;
  addedAt: string;
  lastActivity?: string;
  provider: 'github' | 'gitlab';
}

export interface RepositoriesAPIResponse extends BaseAPIResponse {
  repositories: RepositoryResponse[];
  totalCount: number;
  connectedProviders: string[];
  missingProviders: string[];
  errors?: Array<{
    provider: string;
    error: string;
  }>;
  organizationId: string;
  user: {
    id: string;
    email?: string;
    name?: string;
  };
}

/**
 * Repository sync API types
 */
export interface RepositorySyncResponse extends BaseAPIResponse {
  organization: {
    id: string;
    name: string;
    type: string;
    installationStatus: string;
    totalRepos: number;
    publicRepos: number;
    privateRepos: number;
    lastUpdated: string;
  };
  repositories: RepositoryResponse[];
  syncStatus: {
    success: boolean;
    repositoriesAdded: number;
    repositoriesUpdated: number;
    repositoriesRemoved: number;
    errors: string[];
  };
}

/**
 * Health check API types
 */
export interface HealthCheckResponse extends BaseAPIResponse {
  status: 'ok' | 'error';
  database: {
    status: 'healthy' | 'unhealthy';
    message: string;
  };
  environment: string;
}

/**
 * GitHub installation API types
 */
export interface GitHubInstallationResponse extends BaseAPIResponse {
  installationId: string;
  organizationId: string;
  status: 'success' | 'error';
  message: string;
  redirectUrl?: string;
}

/**
 * Security review API types
 */
export interface SecurityReviewComment {
  body: string;
  file_path?: string;
  line?: number;
  severity?: string;
  comment_type: string;
  finding_ids?: string[];
}

export interface SecurityReviewSummary {
  total_findings: number;
  critical_findings: number;
  high_findings: number;
  success: boolean;
  analyzers_run: number;
}

export interface SecurityReviewRequest {
  pr_info: {
    owner: string;
    repo: string;
    pr_number: number;
    installation_id: string;
  };
  analysis_summary: SecurityReviewSummary;
  comments: SecurityReviewComment[];
  sha?: string;
}

export interface SecurityReviewResponse extends BaseAPIResponse {
  success: boolean;
  message: string;
  comments_posted: number;
  errors: string[];
}

/**
 * Error types for specific scenarios
 */
export interface DatabaseError {
  type: 'database_error';
  message: string;
  code?: string;
  details?: {
    operation?: string;
    collection?: string;
    query?: Record<string, unknown>;
  };
}

export interface ValidationError {
  type: 'validation_error';
  message: string;
  field?: string;
  value?: unknown;
  constraints?: string[];
}

export interface AuthenticationError {
  type: 'authentication_error';
  message: string;
  provider?: string;
  redirectUrl?: string;
}

export interface AuthorizationError {
  type: 'authorization_error';
  message: string;
  requiredPermissions?: string[];
  currentPermissions?: string[];
}

export interface ExternalAPIError {
  type: 'external_api_error';
  message: string;
  provider: string;
  statusCode?: number;
  response?: unknown;
}

export interface RateLimitError {
  type: 'rate_limit_error';
  message: string;
  retryAfter?: number;
  limit?: number;
  remaining?: number;
}

/**
 * Union type for all error types
 */
export type PlatyfendError = 
  | DatabaseError
  | ValidationError
  | AuthenticationError
  | AuthorizationError
  | ExternalAPIError
  | RateLimitError;

/**
 * Request context for error tracking
 */
export interface RequestContext {
  userId?: string;
  organizationId?: string;
  installationId?: string;
  repositoryId?: string;
  operation?: string;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
}
