/**
 * MongoDB Organization Repository Implementation
 * Implements organization repository using MongoDB/Mongoose
 */

import { 
  OrganizationRepository,
  OrganizationEntity,
  CreateOrganizationData,
  UpdateOrganizationData,
  RepositoryInfo,
  RepositorySyncData,
  OrganizationSearchCriteria
} from '../interfaces/organization.repository';
import {
  RepositoryResult,
  PaginatedResult,
  createSuccessResult,
  createErrorResult,
  handleRepositoryError,
  createPaginatedResult,
  QueryOptions
} from '../interfaces/base.repository';
import { Organization } from '@/src/lib/database/models';
import { databaseService } from '@/src/lib/database/service';

/**
 * MongoDB Organization Repository
 */
export class MongoOrganizationRepository implements OrganizationRepository {
  
  /**
   * Ensure database connection before operations
   */
  private async ensureConnection(): Promise<void> {
    await databaseService.ensureConnection();
  }

  /**
   * Convert Mongoose document to entity
   */
  private toEntity(doc: any): OrganizationEntity {
    return {
      id: doc._id.toString(),
      orgId: doc.org_id,
      orgName: doc.org_name,
      provider: doc.provider,
      avatarUrl: doc.avatar_url,
      orgType: doc.org_type,
      description: doc.description,
      publicRepos: doc.public_repos,
      installationId: doc.installation_id,
      installationStatus: doc.installation_status,
      userId: doc.user_id,
      repos: doc.repos || [],
      lastSync: doc.last_sync,
      syncStatus: doc.sync_status || 'idle',
      syncError: doc.sync_error,
      createdAt: doc.created_at || doc.createdAt,
      updatedAt: doc.updated_at || doc.updatedAt,
    };
  }

  /**
   * Convert entity to Mongoose document data
   */
  private toDocumentData(data: CreateOrganizationData | UpdateOrganizationData): any {
    const docData: any = {};
    
    if ('orgId' in data) docData.org_id = data.orgId;
    if ('orgName' in data) docData.org_name = data.orgName;
    if ('provider' in data) docData.provider = data.provider;
    if ('avatarUrl' in data) docData.avatar_url = data.avatarUrl;
    if ('orgType' in data) docData.org_type = data.orgType;
    if ('description' in data) docData.description = data.description;
    if ('publicRepos' in data) docData.public_repos = data.publicRepos;
    if ('installationId' in data) docData.installation_id = data.installationId;
    if ('installationStatus' in data) docData.installation_status = data.installationStatus;
    if ('userId' in data) docData.user_id = data.userId;
    if ('repos' in data) docData.repos = data.repos;
    if ('lastSync' in data) docData.last_sync = data.lastSync;
    if ('syncStatus' in data) docData.sync_status = data.syncStatus;
    if ('syncError' in data) docData.sync_error = data.syncError;

    return docData;
  }

  /**
   * Find entity by ID
   */
  async findById(id: string, options?: QueryOptions): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();
      
      let query = Organization.findById(id);
      
      if (options?.select) {
        query = query.select(options.select.join(' '));
      }
      
      const doc = await query.exec();
      
      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }
      
      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Find entities by criteria
   */
  async find(criteria: Record<string, unknown>, options?: QueryOptions): Promise<RepositoryResult<OrganizationEntity[]>> {
    try {
      await this.ensureConnection();
      
      let query = Organization.find(criteria);
      
      if (options?.select) {
        query = query.select(options.select.join(' '));
      }
      
      if (options?.sort) {
        query = query.sort(options.sort);
      }
      
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      
      if (options?.offset) {
        query = query.skip(options.offset);
      }
      
      const docs = await query.exec();
      const entities = docs.map(doc => this.toEntity(doc));
      
      return createSuccessResult(entities);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Find one entity by criteria
   */
  async findOne(criteria: Record<string, unknown>, options?: QueryOptions): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();
      
      let query = Organization.findOne(criteria);
      
      if (options?.select) {
        query = query.select(options.select.join(' '));
      }
      
      const doc = await query.exec();
      
      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }
      
      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Find entities with pagination
   */
  async findPaginated(
    criteria: Record<string, unknown>,
    page: number,
    limit: number,
    options?: Omit<QueryOptions, 'limit' | 'offset'>
  ): Promise<RepositoryResult<PaginatedResult<OrganizationEntity>>> {
    try {
      await this.ensureConnection();
      
      const offset = (page - 1) * limit;
      
      let query = Organization.find(criteria);
      
      if (options?.select) {
        query = query.select(options.select.join(' '));
      }
      
      if (options?.sort) {
        query = query.sort(options.sort);
      }
      
      query = query.skip(offset).limit(limit);
      
      const [docs, total] = await Promise.all([
        query.exec(),
        Organization.countDocuments(criteria)
      ]);
      
      const entities = docs.map(doc => this.toEntity(doc));
      const paginatedResult = createPaginatedResult(entities, total, page, limit);
      
      return createSuccessResult(paginatedResult);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Create new entity
   */
  async create(data: CreateOrganizationData): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();
      
      const docData = this.toDocumentData(data);
      const doc = new Organization(docData);
      const savedDoc = await doc.save();
      
      return createSuccessResult(this.toEntity(savedDoc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Create multiple entities
   */
  async createMany(data: CreateOrganizationData[]): Promise<RepositoryResult<OrganizationEntity[]>> {
    try {
      await this.ensureConnection();
      
      const docDataArray = data.map(item => this.toDocumentData(item));
      const docs = await Organization.insertMany(docDataArray);
      const entities = docs.map(doc => this.toEntity(doc));
      
      return createSuccessResult(entities);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Update entity by ID
   */
  async update(id: string, data: UpdateOrganizationData): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();
      
      const docData = this.toDocumentData(data);
      docData.updated_at = new Date();
      
      const doc = await Organization.findByIdAndUpdate(
        id,
        { $set: docData },
        { new: true, runValidators: true }
      );
      
      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }
      
      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Update entities by criteria
   */
  async updateMany(criteria: Record<string, unknown>, data: UpdateOrganizationData): Promise<RepositoryResult<number>> {
    try {
      await this.ensureConnection();
      
      const docData = this.toDocumentData(data);
      docData.updated_at = new Date();
      
      const result = await Organization.updateMany(
        criteria,
        { $set: docData },
        { runValidators: true }
      );
      
      return createSuccessResult(result.modifiedCount);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Delete entity by ID
   */
  async delete(id: string): Promise<RepositoryResult<boolean>> {
    try {
      await this.ensureConnection();
      
      const result = await Organization.findByIdAndDelete(id);
      return createSuccessResult(!!result);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Delete entities by criteria
   */
  async deleteMany(criteria: Record<string, unknown>): Promise<RepositoryResult<number>> {
    try {
      await this.ensureConnection();
      
      const result = await Organization.deleteMany(criteria);
      return createSuccessResult(result.deletedCount);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Check if entity exists
   */
  async exists(criteria: Record<string, unknown>): Promise<RepositoryResult<boolean>> {
    try {
      await this.ensureConnection();
      
      const count = await Organization.countDocuments(criteria);
      return createSuccessResult(count > 0);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Count entities by criteria
   */
  async count(criteria: Record<string, unknown>): Promise<RepositoryResult<number>> {
    try {
      await this.ensureConnection();

      const count = await Organization.countDocuments(criteria);
      return createSuccessResult(count);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Bulk operations (placeholder implementation)
   */
  async bulkOperation(operations: Array<{
    operation: 'create' | 'update' | 'delete';
    data?: CreateOrganizationData | UpdateOrganizationData;
    criteria?: Record<string, unknown>;
    id?: string;
  }>): Promise<RepositoryResult<any>> {
    // TODO: Implement bulk operations
    return createErrorResult('Bulk operations not yet implemented');
  }

  /**
   * Find organizations by user ID
   */
  async findByUserId(userId: string): Promise<RepositoryResult<OrganizationEntity[]>> {
    return this.find({ user_id: userId });
  }

  /**
   * Find organization by external org ID and provider
   */
  async findByOrgId(orgId: string, provider: 'github' | 'gitlab'): Promise<RepositoryResult<OrganizationEntity>> {
    return this.findOne({ org_id: orgId, provider });
  }

  /**
   * Find organization by installation ID
   */
  async findByInstallationId(installationId: string): Promise<RepositoryResult<OrganizationEntity>> {
    return this.findOne({ installation_id: installationId });
  }

  /**
   * Find organizations with specific repository
   */
  async findWithRepository(repositoryId: string): Promise<RepositoryResult<OrganizationEntity[]>> {
    return this.find({ 'repos.repo_id': repositoryId });
  }

  /**
   * Search organizations with advanced criteria
   */
  async search(
    criteria: OrganizationSearchCriteria,
    page: number = 1,
    limit: number = 20
  ): Promise<RepositoryResult<PaginatedResult<OrganizationEntity>>> {
    try {
      const mongoQuery: any = {};

      if (criteria.userId) mongoQuery.user_id = criteria.userId;
      if (criteria.provider) mongoQuery.provider = criteria.provider;
      if (criteria.installationStatus) mongoQuery.installation_status = criteria.installationStatus;
      if (criteria.orgType) mongoQuery.org_type = criteria.orgType;
      if (criteria.syncStatus) mongoQuery.sync_status = criteria.syncStatus;

      if (criteria.hasInstallation !== undefined) {
        mongoQuery.installation_id = criteria.hasInstallation ? { $exists: true, $ne: null } : { $exists: false };
      }

      if (criteria.lastSyncBefore || criteria.lastSyncAfter) {
        mongoQuery.last_sync = {};
        if (criteria.lastSyncBefore) mongoQuery.last_sync.$lt = criteria.lastSyncBefore;
        if (criteria.lastSyncAfter) mongoQuery.last_sync.$gt = criteria.lastSyncAfter;
      }

      return this.findPaginated(mongoQuery, page, limit, {
        sort: { updated_at: -1 }
      });
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Add repository to organization
   */
  async addRepository(
    organizationId: string,
    repository: RepositoryInfo
  ): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();

      const doc = await Organization.findByIdAndUpdate(
        organizationId,
        {
          $push: { repos: repository },
          $set: { updated_at: new Date() }
        },
        { new: true, runValidators: true }
      );

      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }

      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Update repository in organization
   */
  async updateRepository(
    organizationId: string,
    repositoryId: string,
    updates: Partial<RepositoryInfo>
  ): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();

      const updateFields: any = {};
      Object.keys(updates).forEach(key => {
        updateFields[`repos.$.${key}`] = (updates as any)[key];
      });
      updateFields.updated_at = new Date();

      const doc = await Organization.findOneAndUpdate(
        { _id: organizationId, 'repos.repo_id': repositoryId },
        { $set: updateFields },
        { new: true, runValidators: true }
      );

      if (!doc) {
        return createErrorResult('Organization or repository not found', 'NOT_FOUND');
      }

      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Remove repository from organization
   */
  async removeRepository(
    organizationId: string,
    repositoryId: string
  ): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();

      const doc = await Organization.findByIdAndUpdate(
        organizationId,
        {
          $pull: { repos: { repo_id: repositoryId } },
          $set: { updated_at: new Date() }
        },
        { new: true }
      );

      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }

      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Sync repositories for organization
   */
  async syncRepositories(
    organizationId: string,
    syncData: RepositorySyncData
  ): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();

      const doc = await Organization.findByIdAndUpdate(
        organizationId,
        {
          $set: {
            repos: syncData.repositories,
            sync_status: syncData.syncStatus,
            last_sync: syncData.lastSync,
            sync_error: syncData.syncError,
            updated_at: new Date()
          }
        },
        { new: true, runValidators: true }
      );

      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }

      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Update installation status
   */
  async updateInstallationStatus(
    installationId: string,
    status: 'active' | 'pending' | 'suspended' | 'deleted'
  ): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();

      const doc = await Organization.findOneAndUpdate(
        { installation_id: installationId },
        {
          $set: {
            installation_status: status,
            updated_at: new Date()
          }
        },
        { new: true, runValidators: true }
      );

      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }

      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Get organizations requiring sync
   */
  async getOrganizationsRequiringSync(
    olderThanHours: number
  ): Promise<RepositoryResult<OrganizationEntity[]>> {
    try {
      const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);

      return this.find({
        $or: [
          { last_sync: { $lt: cutoffDate } },
          { last_sync: { $exists: false } },
          { sync_status: 'error' }
        ],
        installation_status: 'active'
      });
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Get organization statistics
   */
  async getStatistics(userId?: string): Promise<RepositoryResult<{
    totalOrganizations: number;
    activeInstallations: number;
    totalRepositories: number;
    byProvider: Record<string, number>;
    byStatus: Record<string, number>;
    recentActivity: {
      lastDay: number;
      lastWeek: number;
      lastMonth: number;
    };
  }>> {
    try {
      await this.ensureConnection();

      const matchStage: any = {};
      if (userId) matchStage.user_id = userId;

      const pipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalOrganizations: { $sum: 1 },
            activeInstallations: {
              $sum: { $cond: [{ $eq: ['$installation_status', 'active'] }, 1, 0] }
            },
            totalRepositories: { $sum: { $size: { $ifNull: ['$repos', []] } } },
            byProvider: {
              $push: '$provider'
            },
            byStatus: {
              $push: '$installation_status'
            },
            lastDay: {
              $sum: {
                $cond: [
                  { $gte: ['$updated_at', new Date(Date.now() - 24 * 60 * 60 * 1000)] },
                  1,
                  0
                ]
              }
            },
            lastWeek: {
              $sum: {
                $cond: [
                  { $gte: ['$updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] },
                  1,
                  0
                ]
              }
            },
            lastMonth: {
              $sum: {
                $cond: [
                  { $gte: ['$updated_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] },
                  1,
                  0
                ]
              }
            }
          }
        }
      ];

      const result = await Organization.aggregate(pipeline);

      if (result.length === 0) {
        return createSuccessResult({
          totalOrganizations: 0,
          activeInstallations: 0,
          totalRepositories: 0,
          byProvider: {},
          byStatus: {},
          recentActivity: { lastDay: 0, lastWeek: 0, lastMonth: 0 }
        });
      }

      const stats = result[0];

      // Count by provider
      const byProvider: Record<string, number> = {};
      stats.byProvider.forEach((provider: string) => {
        byProvider[provider] = (byProvider[provider] || 0) + 1;
      });

      // Count by status
      const byStatus: Record<string, number> = {};
      stats.byStatus.forEach((status: string) => {
        byStatus[status] = (byStatus[status] || 0) + 1;
      });

      return createSuccessResult({
        totalOrganizations: stats.totalOrganizations,
        activeInstallations: stats.activeInstallations,
        totalRepositories: stats.totalRepositories,
        byProvider,
        byStatus,
        recentActivity: {
          lastDay: stats.lastDay,
          lastWeek: stats.lastWeek,
          lastMonth: stats.lastMonth
        }
      });
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Bulk update repositories
   */
  async bulkUpdateRepositories(
    organizationId: string,
    updates: Array<{
      repositoryId: string;
      updates: Partial<RepositoryInfo>;
    }>
  ): Promise<RepositoryResult<OrganizationEntity>> {
    try {
      await this.ensureConnection();

      // Build bulk update operations
      const bulkOps = updates.map(({ repositoryId, updates: repoUpdates }) => {
        const updateFields: any = {};
        Object.keys(repoUpdates).forEach(key => {
          updateFields[`repos.$.${key}`] = (repoUpdates as any)[key];
        });

        return {
          updateOne: {
            filter: { _id: organizationId, 'repos.repo_id': repositoryId },
            update: { $set: updateFields }
          }
        };
      });

      await Organization.bulkWrite(bulkOps);

      // Return updated organization
      const doc = await Organization.findByIdAndUpdate(
        organizationId,
        { $set: { updated_at: new Date() } },
        { new: true }
      );

      if (!doc) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }

      return createSuccessResult(this.toEntity(doc));
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Get organizations by sync status
   */
  async getBySyncStatus(
    status: 'idle' | 'syncing' | 'error'
  ): Promise<RepositoryResult<OrganizationEntity[]>> {
    return this.find({ sync_status: status });
  }

  /**
   * Mark organization sync as started
   */
  async markSyncStarted(organizationId: string): Promise<RepositoryResult<OrganizationEntity>> {
    return this.update(organizationId, {
      syncStatus: 'syncing',
      syncError: undefined
    });
  }

  /**
   * Mark organization sync as completed
   */
  async markSyncCompleted(
    organizationId: string,
    repositoryCount: number
  ): Promise<RepositoryResult<OrganizationEntity>> {
    return this.update(organizationId, {
      syncStatus: 'idle',
      lastSync: new Date(),
      syncError: undefined
    });
  }

  /**
   * Mark organization sync as failed
   */
  async markSyncFailed(
    organizationId: string,
    error: string
  ): Promise<RepositoryResult<OrganizationEntity>> {
    return this.update(organizationId, {
      syncStatus: 'error',
      syncError: error
    });
  }

  /**
   * Get repository by ID within organization
   */
  async getRepository(
    organizationId: string,
    repositoryId: string
  ): Promise<RepositoryResult<RepositoryInfo>> {
    try {
      await this.ensureConnection();

      const doc = await Organization.findOne(
        { _id: organizationId, 'repos.repo_id': repositoryId },
        { 'repos.$': 1 }
      );

      if (!doc || !doc.repos || doc.repos.length === 0) {
        return createErrorResult('Repository not found', 'NOT_FOUND');
      }

      return createSuccessResult(doc.repos[0]);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Get all repositories for organization
   */
  async getRepositories(organizationId: string): Promise<RepositoryResult<RepositoryInfo[]>> {
    try {
      const result = await this.findById(organizationId, { select: ['repos'] });

      if (!result.success || !result.data) {
        return createErrorResult('Organization not found', 'NOT_FOUND');
      }

      return createSuccessResult(result.data.repos);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }

  /**
   * Check if organization has repository
   */
  async hasRepository(
    organizationId: string,
    repositoryId: string
  ): Promise<RepositoryResult<boolean>> {
    try {
      await this.ensureConnection();

      const count = await Organization.countDocuments({
        _id: organizationId,
        'repos.repo_id': repositoryId
      });

      return createSuccessResult(count > 0);
    } catch (error) {
      return handleRepositoryError(error);
    }
  }
}

// Export singleton instance
export const mongoOrganizationRepository = new MongoOrganizationRepository();
