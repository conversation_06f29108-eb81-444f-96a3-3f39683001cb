/**
 * Organization Repository Interface
 * Defines organization-specific repository operations
 */

import { BaseRepository, BaseEntity, RepositoryResult, PaginatedResult } from './base.repository';

/**
 * Organization entity interface
 */
export interface OrganizationEntity extends BaseEntity {
  orgId: string; // External organization ID (GitHub/GitLab)
  orgName: string;
  provider: 'github' | 'gitlab';
  avatarUrl?: string;
  orgType: 'personal' | 'organization';
  description?: string;
  publicRepos?: number;
  installationId?: string;
  installationStatus: 'active' | 'pending' | 'suspended' | 'deleted';
  userId: string; // User who connected this organization
  repos: RepositoryInfo[];
  lastSync?: Date;
  syncStatus?: 'idle' | 'syncing' | 'error';
  syncError?: string;
}

/**
 * Repository information within organization
 */
export interface RepositoryInfo {
  repoId: string; // External repository ID
  name: string;
  fullName: string;
  description?: string;
  private: boolean;
  language?: string;
  stars: number;
  forks: number;
  url?: string;
  defaultBranch?: string;
  lastSync: Date;
  addedAt: Date;
  lastActivity?: Date;
  archived?: boolean;
}

/**
 * Organization creation data
 */
export interface CreateOrganizationData {
  orgId: string;
  orgName: string;
  provider: 'github' | 'gitlab';
  avatarUrl?: string;
  orgType: 'personal' | 'organization';
  description?: string;
  publicRepos?: number;
  installationId?: string;
  installationStatus: 'active' | 'pending' | 'suspended' | 'deleted';
  userId: string;
  repos?: RepositoryInfo[];
}

/**
 * Organization update data
 */
export interface UpdateOrganizationData {
  orgName?: string;
  avatarUrl?: string;
  description?: string;
  publicRepos?: number;
  installationId?: string;
  installationStatus?: 'active' | 'pending' | 'suspended' | 'deleted';
  lastSync?: Date;
  syncStatus?: 'idle' | 'syncing' | 'error';
  syncError?: string;
}

/**
 * Repository sync data
 */
export interface RepositorySyncData {
  repositories: RepositoryInfo[];
  syncStatus: 'idle' | 'syncing' | 'error';
  lastSync: Date;
  syncError?: string;
}

/**
 * Organization search criteria
 */
export interface OrganizationSearchCriteria {
  userId?: string;
  provider?: 'github' | 'gitlab';
  installationStatus?: 'active' | 'pending' | 'suspended' | 'deleted';
  orgType?: 'personal' | 'organization';
  hasInstallation?: boolean;
  syncStatus?: 'idle' | 'syncing' | 'error';
  lastSyncBefore?: Date;
  lastSyncAfter?: Date;
}

/**
 * Organization repository interface
 */
export interface OrganizationRepository extends BaseRepository<
  OrganizationEntity,
  CreateOrganizationData,
  UpdateOrganizationData
> {
  /**
   * Find organizations by user ID
   */
  findByUserId(userId: string): Promise<RepositoryResult<OrganizationEntity[]>>;

  /**
   * Find organization by external org ID and provider
   */
  findByOrgId(orgId: string, provider: 'github' | 'gitlab'): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Find organization by installation ID
   */
  findByInstallationId(installationId: string): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Find organizations with specific repository
   */
  findWithRepository(repositoryId: string): Promise<RepositoryResult<OrganizationEntity[]>>;

  /**
   * Search organizations with advanced criteria
   */
  search(
    criteria: OrganizationSearchCriteria,
    page?: number,
    limit?: number
  ): Promise<RepositoryResult<PaginatedResult<OrganizationEntity>>>;

  /**
   * Add repository to organization
   */
  addRepository(
    organizationId: string,
    repository: RepositoryInfo
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Update repository in organization
   */
  updateRepository(
    organizationId: string,
    repositoryId: string,
    updates: Partial<RepositoryInfo>
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Remove repository from organization
   */
  removeRepository(
    organizationId: string,
    repositoryId: string
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Sync repositories for organization
   */
  syncRepositories(
    organizationId: string,
    syncData: RepositorySyncData
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Update installation status
   */
  updateInstallationStatus(
    installationId: string,
    status: 'active' | 'pending' | 'suspended' | 'deleted'
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Get organizations requiring sync
   */
  getOrganizationsRequiringSync(
    olderThanHours: number
  ): Promise<RepositoryResult<OrganizationEntity[]>>;

  /**
   * Get organization statistics
   */
  getStatistics(userId?: string): Promise<RepositoryResult<{
    totalOrganizations: number;
    activeInstallations: number;
    totalRepositories: number;
    byProvider: Record<string, number>;
    byStatus: Record<string, number>;
    recentActivity: {
      lastDay: number;
      lastWeek: number;
      lastMonth: number;
    };
  }>>;

  /**
   * Bulk update repositories
   */
  bulkUpdateRepositories(
    organizationId: string,
    updates: Array<{
      repositoryId: string;
      updates: Partial<RepositoryInfo>;
    }>
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Get organizations by sync status
   */
  getBySyncStatus(
    status: 'idle' | 'syncing' | 'error'
  ): Promise<RepositoryResult<OrganizationEntity[]>>;

  /**
   * Mark organization sync as started
   */
  markSyncStarted(organizationId: string): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Mark organization sync as completed
   */
  markSyncCompleted(
    organizationId: string,
    repositoryCount: number
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Mark organization sync as failed
   */
  markSyncFailed(
    organizationId: string,
    error: string
  ): Promise<RepositoryResult<OrganizationEntity>>;

  /**
   * Get repository by ID within organization
   */
  getRepository(
    organizationId: string,
    repositoryId: string
  ): Promise<RepositoryResult<RepositoryInfo>>;

  /**
   * Get all repositories for organization
   */
  getRepositories(organizationId: string): Promise<RepositoryResult<RepositoryInfo[]>>;

  /**
   * Check if organization has repository
   */
  hasRepository(
    organizationId: string,
    repositoryId: string
  ): Promise<RepositoryResult<boolean>>;
}
