/**
 * Base Repository Interface
 * Defines common repository operations for all entities
 */

/**
 * Base entity interface
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Query options for repository operations
 */
export interface QueryOptions {
  limit?: number;
  offset?: number;
  sort?: Record<string, 1 | -1>;
  select?: string[];
  populate?: string[];
}

/**
 * Pagination result
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Repository operation result
 */
export interface RepositoryResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult<T> {
  success: boolean;
  created: T[];
  updated: T[];
  deleted: string[];
  errors: Array<{
    operation: 'create' | 'update' | 'delete';
    id?: string;
    error: string;
  }>;
}

/**
 * Base repository interface with common CRUD operations
 */
export interface BaseRepository<T extends BaseEntity, CreateData = Partial<T>, UpdateData = Partial<T>> {
  /**
   * Find entity by ID
   */
  findById(id: string, options?: QueryOptions): Promise<RepositoryResult<T>>;

  /**
   * Find entities by criteria
   */
  find(criteria: Record<string, unknown>, options?: QueryOptions): Promise<RepositoryResult<T[]>>;

  /**
   * Find one entity by criteria
   */
  findOne(criteria: Record<string, unknown>, options?: QueryOptions): Promise<RepositoryResult<T>>;

  /**
   * Find entities with pagination
   */
  findPaginated(
    criteria: Record<string, unknown>,
    page: number,
    limit: number,
    options?: Omit<QueryOptions, 'limit' | 'offset'>
  ): Promise<RepositoryResult<PaginatedResult<T>>>;

  /**
   * Create new entity
   */
  create(data: CreateData): Promise<RepositoryResult<T>>;

  /**
   * Create multiple entities
   */
  createMany(data: CreateData[]): Promise<RepositoryResult<T[]>>;

  /**
   * Update entity by ID
   */
  update(id: string, data: UpdateData): Promise<RepositoryResult<T>>;

  /**
   * Update entities by criteria
   */
  updateMany(criteria: Record<string, unknown>, data: UpdateData): Promise<RepositoryResult<number>>;

  /**
   * Delete entity by ID
   */
  delete(id: string): Promise<RepositoryResult<boolean>>;

  /**
   * Delete entities by criteria
   */
  deleteMany(criteria: Record<string, unknown>): Promise<RepositoryResult<number>>;

  /**
   * Check if entity exists
   */
  exists(criteria: Record<string, unknown>): Promise<RepositoryResult<boolean>>;

  /**
   * Count entities by criteria
   */
  count(criteria: Record<string, unknown>): Promise<RepositoryResult<number>>;

  /**
   * Bulk operations
   */
  bulkOperation(operations: Array<{
    operation: 'create' | 'update' | 'delete';
    data?: CreateData | UpdateData;
    criteria?: Record<string, unknown>;
    id?: string;
  }>): Promise<RepositoryResult<BulkOperationResult<T>>>;
}

/**
 * Repository error types
 */
export enum RepositoryErrorType {
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DUPLICATE_KEY = 'DUPLICATE_KEY',
  DATABASE_ERROR = 'DATABASE_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  INVALID_OPERATION = 'INVALID_OPERATION',
}

/**
 * Repository error class
 */
export class RepositoryError extends Error {
  constructor(
    message: string,
    public type: RepositoryErrorType,
    public code?: string,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'RepositoryError';
  }
}

/**
 * Helper function to create repository results
 */
export function createRepositoryResult<T>(
  data?: T,
  error?: string,
  code?: string
): RepositoryResult<T> {
  return {
    success: !error,
    data,
    error,
    code,
  };
}

/**
 * Helper function to create successful repository result
 */
export function createSuccessResult<T>(data: T): RepositoryResult<T> {
  return {
    success: true,
    data,
  };
}

/**
 * Helper function to create error repository result
 */
export function createErrorResult<T>(
  error: string,
  code?: string
): RepositoryResult<T> {
  return {
    success: false,
    error,
    code,
  };
}

/**
 * Helper function to handle repository errors
 */
export function handleRepositoryError<T>(error: unknown): RepositoryResult<T> {
  if (error instanceof RepositoryError) {
    return createErrorResult(error.message, error.code);
  }

  if (error instanceof Error) {
    // Handle specific database errors
    if (error.message.includes('duplicate key')) {
      return createErrorResult(
        'Resource already exists',
        RepositoryErrorType.DUPLICATE_KEY
      );
    }

    if (error.message.includes('validation')) {
      return createErrorResult(
        'Validation failed',
        RepositoryErrorType.VALIDATION_ERROR
      );
    }

    return createErrorResult(
      error.message,
      RepositoryErrorType.DATABASE_ERROR
    );
  }

  return createErrorResult(
    'Unknown error occurred',
    RepositoryErrorType.DATABASE_ERROR
  );
}

/**
 * Helper function to create paginated result
 */
export function createPaginatedResult<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginatedResult<T> {
  const totalPages = Math.ceil(total / limit);
  
  return {
    data,
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}
