/**
 * Repository Factory
 * Provides centralized access to repository instances
 */

import { OrganizationRepository } from './interfaces/organization.repository';
import { mongoOrganizationRepository } from './implementations/mongo-organization.repository';

/**
 * Repository configuration
 */
export interface RepositoryConfig {
  type: 'mongodb' | 'postgresql' | 'memory';
  connectionString?: string;
  options?: Record<string, unknown>;
}

/**
 * Repository factory class
 */
export class RepositoryFactory {
  private static instance: RepositoryFactory;
  private config: RepositoryConfig;
  private repositories: Map<string, unknown> = new Map();

  private constructor(config: RepositoryConfig) {
    this.config = config;
  }

  /**
   * Initialize the repository factory
   */
  public static initialize(config: RepositoryConfig): RepositoryFactory {
    if (!RepositoryFactory.instance) {
      RepositoryFactory.instance = new RepositoryFactory(config);
    }
    return RepositoryFactory.instance;
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): RepositoryFactory {
    if (!RepositoryFactory.instance) {
      // Default to MongoDB configuration
      RepositoryFactory.instance = new RepositoryFactory({ type: 'mongodb' });
    }
    return RepositoryFactory.instance;
  }

  /**
   * Get organization repository
   */
  public getOrganizationRepository(): OrganizationRepository {
    const key = 'organization';
    
    if (!this.repositories.has(key)) {
      let repository: OrganizationRepository;

      switch (this.config.type) {
        case 'mongodb':
          repository = mongoOrganizationRepository;
          break;
        case 'postgresql':
          // TODO: Implement PostgreSQL repository
          throw new Error('PostgreSQL repository not yet implemented');
        case 'memory':
          // TODO: Implement in-memory repository for testing
          throw new Error('Memory repository not yet implemented');
        default:
          throw new Error(`Unsupported repository type: ${this.config.type}`);
      }

      this.repositories.set(key, repository);
    }

    return this.repositories.get(key) as OrganizationRepository;
  }

  /**
   * Clear all cached repositories (useful for testing)
   */
  public clearCache(): void {
    this.repositories.clear();
  }

  /**
   * Get current configuration
   */
  public getConfig(): RepositoryConfig {
    return { ...this.config };
  }

  /**
   * Update configuration (clears cache)
   */
  public updateConfig(config: Partial<RepositoryConfig>): void {
    this.config = { ...this.config, ...config };
    this.clearCache();
  }
}

// Default factory instance
export const repositoryFactory = RepositoryFactory.getInstance();

// Convenience functions for accessing repositories
export function getOrganizationRepository(): OrganizationRepository {
  return repositoryFactory.getOrganizationRepository();
}

// Export repository interfaces for external use
export type { OrganizationRepository } from './interfaces/organization.repository';
export type { 
  OrganizationEntity,
  CreateOrganizationData,
  UpdateOrganizationData,
  RepositoryInfo,
  RepositorySyncData,
  OrganizationSearchCriteria
} from './interfaces/organization.repository';

export type {
  BaseRepository,
  BaseEntity,
  QueryOptions,
  PaginatedResult,
  RepositoryResult,
  BulkOperationResult
} from './interfaces/base.repository';

export {
  RepositoryError,
  RepositoryErrorType,
  createRepositoryResult,
  createSuccessResult,
  createErrorResult,
  handleRepositoryError,
  createPaginatedResult
} from './interfaces/base.repository';
