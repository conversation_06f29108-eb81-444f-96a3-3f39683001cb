/**
 * Performance Monitoring Service
 * Tracks and analyzes application performance metrics
 */

/**
 * Performance metric types
 */
export type MetricType = 
  | 'api_request'
  | 'database_query'
  | 'component_render'
  | 'page_load'
  | 'user_interaction'
  | 'webhook_processing'
  | 'background_task'
  | 'memory_usage'
  | 'custom';

/**
 * Performance metric data
 */
export interface PerformanceMetric {
  id: string;
  type: MetricType;
  name: string;
  duration: number;
  timestamp: Date;
  
  // Context information
  context: {
    userId?: string;
    sessionId?: string;
    requestId?: string;
    component?: string;
    operation?: string;
    endpoint?: string;
    method?: string;
  };
  
  // Performance details
  details: {
    startTime: number;
    endTime: number;
    memoryUsage?: {
      used: number;
      total: number;
      percentage: number;
    };
    networkInfo?: {
      effectiveType?: string;
      downlink?: number;
      rtt?: number;
    };
    deviceInfo?: {
      userAgent?: string;
      platform?: string;
      cores?: number;
    };
  };
  
  // Categorization
  tags: string[];
  severity: 'normal' | 'slow' | 'critical';
  
  // Additional metadata
  metadata?: Record<string, unknown>;
}

/**
 * Performance thresholds configuration
 */
export interface PerformanceThresholds {
  api_request: { slow: number; critical: number };
  database_query: { slow: number; critical: number };
  component_render: { slow: number; critical: number };
  page_load: { slow: number; critical: number };
  user_interaction: { slow: number; critical: number };
  webhook_processing: { slow: number; critical: number };
  background_task: { slow: number; critical: number };
  memory_usage: { slow: number; critical: number };
  custom: { slow: number; critical: number };
}

/**
 * Performance statistics
 */
export interface PerformanceStats {
  totalMetrics: number;
  averageDuration: number;
  medianDuration: number;
  p95Duration: number;
  p99Duration: number;
  slowOperations: number;
  criticalOperations: number;
  operationsPerSecond: number;
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceConfig {
  enabled: boolean;
  sampling: {
    rate: number; // 0-1, percentage of operations to track
    alwaysTrackSlow: boolean;
    alwaysTrackCritical: boolean;
  };
  thresholds: PerformanceThresholds;
  storage: {
    maxMetrics: number;
    retentionHours: number;
  };
  reporting: {
    enabled: boolean;
    intervalMinutes: number;
    endpoint?: string;
  };
}

/**
 * Default performance thresholds (in milliseconds)
 */
const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  api_request: { slow: 1000, critical: 5000 },
  database_query: { slow: 500, critical: 2000 },
  component_render: { slow: 100, critical: 500 },
  page_load: { slow: 2000, critical: 5000 },
  user_interaction: { slow: 100, critical: 300 },
  webhook_processing: { slow: 2000, critical: 10000 },
  background_task: { slow: 5000, critical: 30000 },
  memory_usage: { slow: 100, critical: 500 }, // Memory usage in MB
  custom: { slow: 1000, critical: 5000 },
};

/**
 * Performance Monitor Service
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private activeOperations = new Map<string, { startTime: number; context: any }>();
  private config: PerformanceConfig;
  private reportingInterval?: NodeJS.Timeout;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enabled: true,
      sampling: {
        rate: 1.0,
        alwaysTrackSlow: true,
        alwaysTrackCritical: true,
      },
      thresholds: DEFAULT_THRESHOLDS,
      storage: {
        maxMetrics: 10000,
        retentionHours: 24,
      },
      reporting: {
        enabled: false,
        intervalMinutes: 5,
      },
      ...config,
    };

    // Start automatic reporting if enabled
    if (this.config.reporting.enabled) {
      this.startReporting();
    }

    // Start cleanup interval
    this.startCleanup();
  }

  /**
   * Start tracking an operation
   */
  startOperation(
    type: MetricType,
    name: string,
    context: PerformanceMetric['context'] = {},
    metadata?: Record<string, unknown>
  ): string {
    if (!this.config.enabled) {
      return '';
    }

    const operationId = this.generateOperationId();
    const startTime = performance.now();

    this.activeOperations.set(operationId, {
      startTime,
      context: {
        type,
        name,
        context,
        metadata,
      },
    });

    return operationId;
  }

  /**
   * End tracking an operation
   */
  endOperation(operationId: string): PerformanceMetric | null {
    if (!this.config.enabled || !operationId) {
      return null;
    }

    const operation = this.activeOperations.get(operationId);
    if (!operation) {
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - operation.startTime;

    // Remove from active operations
    this.activeOperations.delete(operationId);

    // Create metric
    const metric = this.createMetric(
      operation.context.type,
      operation.context.name,
      duration,
      operation.startTime,
      endTime,
      operation.context.context,
      operation.context.metadata
    );

    // Apply sampling
    if (this.shouldTrackMetric(metric)) {
      this.recordMetric(metric);
      return metric;
    }

    return null;
  }

  /**
   * Record a one-time metric
   */
  recordMetric(metric: PerformanceMetric): void {
    if (!this.config.enabled) {
      return;
    }

    this.metrics.push(metric);

    // Enforce storage limits
    if (this.metrics.length > this.config.storage.maxMetrics) {
      this.metrics = this.metrics.slice(-this.config.storage.maxMetrics);
    }

    // Log slow/critical operations
    if (metric.severity !== 'normal') {
      this.logSlowOperation(metric);
    }
  }

  /**
   * Create a performance metric
   */
  private createMetric(
    type: MetricType,
    name: string,
    duration: number,
    startTime: number,
    endTime: number,
    context: PerformanceMetric['context'] = {},
    metadata?: Record<string, unknown>
  ): PerformanceMetric {
    const severity = this.calculateSeverity(type, duration);
    const memoryUsage = this.getMemoryUsage();
    const networkInfo = this.getNetworkInfo();
    const deviceInfo = this.getDeviceInfo();

    return {
      id: this.generateMetricId(),
      type,
      name,
      duration,
      timestamp: new Date(),
      context,
      details: {
        startTime,
        endTime,
        memoryUsage,
        networkInfo,
        deviceInfo,
      },
      tags: [type, severity, ...(context.component ? [context.component] : [])],
      severity,
      metadata,
    };
  }

  /**
   * Calculate severity based on thresholds
   */
  private calculateSeverity(type: MetricType, duration: number): 'normal' | 'slow' | 'critical' {
    const thresholds = this.config.thresholds[type] || this.config.thresholds.custom;
    
    if (duration >= thresholds.critical) {
      return 'critical';
    } else if (duration >= thresholds.slow) {
      return 'slow';
    }
    
    return 'normal';
  }

  /**
   * Check if metric should be tracked based on sampling
   */
  private shouldTrackMetric(metric: PerformanceMetric): boolean {
    // Always track slow/critical operations if configured
    if (metric.severity === 'slow' && this.config.sampling.alwaysTrackSlow) {
      return true;
    }
    
    if (metric.severity === 'critical' && this.config.sampling.alwaysTrackCritical) {
      return true;
    }

    // Apply sampling rate
    return Math.random() < this.config.sampling.rate;
  }

  /**
   * Get memory usage information
   */
  private getMemoryUsage(): PerformanceMetric['details']['memoryUsage'] {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
      };
    }
    return undefined;
  }

  /**
   * Get network information
   */
  private getNetworkInfo(): PerformanceMetric['details']['networkInfo'] {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
      };
    }
    return undefined;
  }

  /**
   * Get device information
   */
  private getDeviceInfo(): PerformanceMetric['details']['deviceInfo'] {
    if (typeof navigator !== 'undefined') {
      return {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        cores: navigator.hardwareConcurrency,
      };
    }
    return undefined;
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Generate unique metric ID
   */
  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Log slow operations
   */
  private logSlowOperation(metric: PerformanceMetric): void {
    const logLevel = metric.severity === 'critical' ? 'error' : 'warn';
    const message = `${metric.severity.toUpperCase()} Performance: ${metric.name} took ${metric.duration.toFixed(2)}ms`;
    
    console[logLevel](message, {
      type: metric.type,
      context: metric.context,
      thresholds: this.config.thresholds[metric.type],
    });
  }

  /**
   * Get performance statistics
   */
  getStats(
    timeRangeMinutes: number = 60,
    type?: MetricType
  ): PerformanceStats {
    const cutoffTime = new Date(Date.now() - timeRangeMinutes * 60 * 1000);
    
    let filteredMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
    
    if (type) {
      filteredMetrics = filteredMetrics.filter(m => m.type === type);
    }

    if (filteredMetrics.length === 0) {
      return {
        totalMetrics: 0,
        averageDuration: 0,
        medianDuration: 0,
        p95Duration: 0,
        p99Duration: 0,
        slowOperations: 0,
        criticalOperations: 0,
        operationsPerSecond: 0,
        timeRange: {
          start: cutoffTime,
          end: new Date(),
        },
      };
    }

    const durations = filteredMetrics.map(m => m.duration).sort((a, b) => a - b);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);
    
    return {
      totalMetrics: filteredMetrics.length,
      averageDuration: totalDuration / filteredMetrics.length,
      medianDuration: this.getPercentile(durations, 50),
      p95Duration: this.getPercentile(durations, 95),
      p99Duration: this.getPercentile(durations, 99),
      slowOperations: filteredMetrics.filter(m => m.severity === 'slow').length,
      criticalOperations: filteredMetrics.filter(m => m.severity === 'critical').length,
      operationsPerSecond: filteredMetrics.length / (timeRangeMinutes * 60),
      timeRange: {
        start: cutoffTime,
        end: new Date(),
      },
    };
  }

  /**
   * Calculate percentile
   */
  private getPercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, index)];
  }

  /**
   * Start automatic reporting
   */
  private startReporting(): void {
    if (this.reportingInterval) {
      clearInterval(this.reportingInterval);
    }

    this.reportingInterval = setInterval(() => {
      this.sendReport();
    }, this.config.reporting.intervalMinutes * 60 * 1000);
  }

  /**
   * Send performance report
   */
  private async sendReport(): Promise<void> {
    if (!this.config.reporting.endpoint) {
      return;
    }

    try {
      const stats = this.getStats(this.config.reporting.intervalMinutes);
      
      await fetch(this.config.reporting.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          stats,
          recentMetrics: this.metrics.slice(-100), // Send last 100 metrics
        }),
      });
    } catch (error) {
      console.error('Failed to send performance report:', error);
    }
  }

  /**
   * Start cleanup interval
   */
  private startCleanup(): void {
    setInterval(() => {
      this.cleanup();
    }, 60 * 60 * 1000); // Run every hour
  }

  /**
   * Clean up old metrics
   */
  private cleanup(): void {
    const cutoffTime = new Date(Date.now() - this.config.storage.retentionHours * 60 * 60 * 1000);
    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
  }

  /**
   * Get all metrics
   */
  getMetrics(type?: MetricType, limit?: number): PerformanceMetric[] {
    let filtered = type ? this.metrics.filter(m => m.type === type) : this.metrics;
    
    if (limit) {
      filtered = filtered.slice(-limit);
    }
    
    return filtered;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (config.reporting?.enabled && !this.reportingInterval) {
      this.startReporting();
    } else if (config.reporting?.enabled === false && this.reportingInterval) {
      clearInterval(this.reportingInterval);
      this.reportingInterval = undefined;
    }
  }

  /**
   * Destroy monitor and clean up resources
   */
  destroy(): void {
    if (this.reportingInterval) {
      clearInterval(this.reportingInterval);
    }
    
    this.metrics = [];
    this.activeOperations.clear();
  }
}
