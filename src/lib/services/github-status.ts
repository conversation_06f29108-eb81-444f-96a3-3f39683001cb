import { githubAppAuth } from '@/src/lib/github/app-auth';

/**
 * GitHub Status Check states
 */
export enum GitHubStatusState {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILURE = 'failure',
  ERROR = 'error'
}

/**
 * GitHub Status Check context (appears as the check name)
 */
export const PLATYFEND_STATUS_CONTEXT = 'platyfend/security-review';

/**
 * Service for managing GitHub status checks
 * Creates the "running" status that appears in PRs like CodeRabbit
 */
export class GitHubStatusService {
  /**
   * Create or update a status check on a commit
   */
  async createStatus(
    installationId: string,
    owner: string,
    repo: string,
    sha: string,
    state: GitHubStatusState,
    description: string,
    targetUrl?: string
  ): Promise<boolean> {
    try {
      const { headers } = await githubAppAuth.createAPIClient(installationId);
      
      const statusData = {
        state,
        description,
        context: PLATYFEND_STATUS_CONTEXT,
        ...(targetUrl && { target_url: targetUrl })
      };

      const response = await fetch(
        `https://api.github.com/repos/${owner}/${repo}/statuses/${sha}`,
        {
          method: 'POST',
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(statusData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`Failed to create GitHub status: ${response.status}`, errorData);
        return false;
      }

      console.log(`GitHub status created: ${state} for ${owner}/${repo}@${sha}`);
      return true;
    } catch (error) {
      console.error('Error creating GitHub status:', error);
      return false;
    }
  }

  /**
   * Set status to "pending" when PR is opened/reopened
   */
  async setPendingStatus(
    installationId: string,
    owner: string,
    repo: string,
    sha: string,
    prNumber: number
  ): Promise<boolean> {
    const description = 'PlatyfendApp is analyzing your code...';
    const targetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/review/${owner}/${repo}/pull/${prNumber}`;
    
    return this.createStatus(
      installationId,
      owner,
      repo,
      sha,
      GitHubStatusState.PENDING,
      description,
      targetUrl
    );
  }

  /**
   * Set status to "success" when review is complete
   */
  async setSuccessStatus(
    installationId: string,
    owner: string,
    repo: string,
    sha: string,
    prNumber: number,
    issuesFound: number = 0
  ): Promise<boolean> {
    const description = issuesFound > 0 
      ? `PlatyfendApp found ${issuesFound} security issue${issuesFound === 1 ? '' : 's'}`
      : 'PlatyfendApp completed - no security issues found';
    
    const targetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/review/${owner}/${repo}/pull/${prNumber}`;
    
    return this.createStatus(
      installationId,
      owner,
      repo,
      sha,
      GitHubStatusState.SUCCESS,
      description,
      targetUrl
    );
  }

  /**
   * Set status to "failure" when review fails
   */
  async setFailureStatus(
    installationId: string,
    owner: string,
    repo: string,
    sha: string,
    prNumber: number,
    errorMessage?: string
  ): Promise<boolean> {
    const description = errorMessage 
      ? `PlatyfendApp failed: ${errorMessage}`
      : 'PlatyfendApp encountered an error during analysis';
    
    const targetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/review/${owner}/${repo}/pull/${prNumber}`;
    
    return this.createStatus(
      installationId,
      owner,
      repo,
      sha,
      GitHubStatusState.FAILURE,
      description,
      targetUrl
    );
  }

  /**
   * Get the installation ID for a repository
   */
  async getInstallationIdForRepo(owner: string, repo: string): Promise<string | null> {
    try {
      // Find organization that has this repository
      const organization = await import('@/src/lib/database/models').then(({ Organization }) =>
        Organization.findOne({
          'repos.name': repo,
        })
      );

      return organization?.installation_id || null;
    } catch (error) {
      console.error('Error finding installation for repo:', error);
      return null;
    }
  }
}

// Singleton instance
export const githubStatusService = new GitHubStatusService();

// Helper functions
export async function setPendingStatusForPR(
  owner: string,
  repo: string,
  sha: string,
  prNumber: number
): Promise<boolean> {
  const installationId = await githubStatusService.getInstallationIdForRepo(owner, repo);
  if (!installationId) {
    console.warn(`No installation found for ${owner}/${repo}`);
    return false;
  }

  return githubStatusService.setPendingStatus(installationId, owner, repo, sha, prNumber);
}

export async function setSuccessStatusForPR(
  owner: string,
  repo: string,
  sha: string,
  prNumber: number,
  issuesFound: number = 0
): Promise<boolean> {
  const installationId = await githubStatusService.getInstallationIdForRepo(owner, repo);
  if (!installationId) {
    console.warn(`No installation found for ${owner}/${repo}`);
    return false;
  }

  return githubStatusService.setSuccessStatus(installationId, owner, repo, sha, prNumber, issuesFound);
}

export async function setFailureStatusForPR(
  owner: string,
  repo: string,
  sha: string,
  prNumber: number,
  errorMessage?: string
): Promise<boolean> {
  const installationId = await githubStatusService.getInstallationIdForRepo(owner, repo);
  if (!installationId) {
    console.warn(`No installation found for ${owner}/${repo}`);
    return false;
  }

  return githubStatusService.setFailureStatus(installationId, owner, repo, sha, prNumber, errorMessage);
}
