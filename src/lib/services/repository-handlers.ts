import { Organization } from '@/src/lib/database/models';
import { WebhookSyncResult } from './webhook-types';
import { databaseService } from '@/src/lib/database/service';

/**
 * Repository webhook handlers
 * Handles GitHub repository lifecycle events
 */
export class RepositoryHandlers {
  /**
   * Handle repository renamed
   */
  async handleRepositoryRenamed(
    organization: any,
    repoId: string,
    repository: any,
    result: WebhookSyncResult
  ): Promise<void> {
    // Ensure database connection
    await databaseService.ensureConnection();

    const updateResult = await Organization.updateOne(
      {
        _id: organization._id,
        'repos.repo_id': repoId
      },
      {
        $set: {
          'repos.$.name': repository.name,
          'repos.$.full_name': repository.full_name,
          'repos.$.last_sync': new Date(),
          updated_at: new Date()
        }
      }
    );

    if (updateResult.modifiedCount > 0) {
      result.repositoriesAffected++;
    }
  }

  /**
   * Handle repository transferred
   */
  async handleRepositoryTransferred(
    organization: any,
    repoId: string,
    repository: any,
    result: WebhookSyncResult
  ): Promise<void> {
    // Ensure database connection
    await databaseService.ensureConnection();

    const updateResult = await Organization.updateOne(
      { 
        _id: organization._id,
        'repos.repo_id': repoId 
      },
      {
        $set: {
          'repos.$.full_name': repository.full_name,
          'repos.$.last_sync': new Date(),
          updated_at: new Date()
        }
      }
    );

    if (updateResult.modifiedCount > 0) {
      result.repositoriesAffected++;
    }
  }

  /**
   * Handle repository visibility changed
   */
  async handleRepositoryVisibilityChanged(
    organization: any,
    repoId: string,
    repository: any,
    result: WebhookSyncResult
  ): Promise<void> {
    // Ensure database connection
    await databaseService.ensureConnection();

    const updateResult = await Organization.updateOne(
      { 
        _id: organization._id,
        'repos.repo_id': repoId 
      },
      {
        $set: {
          'repos.$.private': repository.private,
          'repos.$.last_sync': new Date(),
          updated_at: new Date()
        }
      }
    );

    if (updateResult.modifiedCount > 0) {
      result.repositoriesAffected++;
    }
  }

  /**
   * Handle repository deleted
   */
  async handleRepositoryDeleted(
    organization: any,
    repoId: string,
    result: WebhookSyncResult
  ): Promise<void> {
    // Ensure database connection
    await databaseService.ensureConnection();

    await organization.removeRepository(repoId);
    result.repositoriesAffected++;
  }

  /**
   * Handle repositories added to installation
   */
  async handleRepositoriesAdded(installationId: string, repositories: any[]): Promise<void> {
    console.log(`Adding ${repositories.length} repositories to installation ${installationId}`);
    // TODO: Implement repository addition logic
  }

  /**
   * Handle repositories removed from installation
   */
  async handleRepositoriesRemoved(installationId: string, repositories: any[]): Promise<void> {
    console.log(`Removing ${repositories.length} repositories from installation ${installationId}`);
    // TODO: Implement repository removal logic
  }

  /**
   * Handle repository created
   */
  async handleRepositoryCreated(
    organization: any,
    repository: any,
    result: WebhookSyncResult
  ): Promise<void> {
    console.log(`Repository ${repository.name} created`);
    result.repositoriesAffected = 1;
    result.success = true;
    // TODO: Implement repository creation logic
  }

  /**
   * Handle repository updated
   */
  async handleRepositoryUpdated(
    organization: any,
    repository: any,
    result: WebhookSyncResult
  ): Promise<void> {
    console.log(`Repository ${repository.name} updated`);
    result.repositoriesAffected = 1;
    result.success = true;
    // TODO: Implement repository update logic
  }
}
