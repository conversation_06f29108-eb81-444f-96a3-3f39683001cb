import { githubAppAuth } from '@/src/lib/github/app-auth';
import { githubStatusService } from './github-status';
import {
  SecurityComment,
  SecurityAnalysisSummary,
  SecurityPRInfo
} from '@/src/lib/types/security-review';

// Re-export types for backward compatibility
export type GitHubComment = SecurityComment;
export type SecurityReviewSummary = SecurityAnalysisSummary;
export type PRInfo = SecurityPRInfo;

/**
 * Service for posting security review comments to GitHub PRs
 */
export class GitHubCommentService {
  /**
   * Get the latest commit SHA for a PR
   */
  async getPRCommitSHA(
    installationId: string,
    owner: string,
    repo: string,
    prNumber: number
  ): Promise<string | null> {
    try {
      const { headers } = await githubAppAuth.createAPIClient(installationId);

      const response = await fetch(
        `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}`,
        { headers }
      );

      if (!response.ok) {
        console.error(`Failed to get PR details: ${response.status}`);
        return null;
      }

      const prData = await response.json();
      return prData.head?.sha || null;
    } catch (error) {
      console.error('Error getting PR commit SHA:', error);
      return null;
    }
  }
  /**
   * Post a general comment on a PR
   */
  async postPRComment(
    installationId: string,
    owner: string,
    repo: string,
    prNumber: number,
    body: string
  ): Promise<boolean> {
    try {
      const { headers } = await githubAppAuth.createAPIClient(installationId);
      
      const response = await fetch(
        `https://api.github.com/repos/${owner}/${repo}/issues/${prNumber}/comments`,
        {
          method: 'POST',
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ body }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`Failed to post PR comment: ${response.status}`, errorData);
        return false;
      }

      console.log(`PR comment posted successfully for ${owner}/${repo}#${prNumber}`);
      return true;
    } catch (error) {
      console.error('Error posting PR comment:', error);
      return false;
    }
  }

  /**
   * Post a review comment on a specific line of a file in a PR
   */
  async postReviewComment(
    installationId: string,
    owner: string,
    repo: string,
    prNumber: number,
    body: string,
    filePath: string,
    line: number,
    sha: string
  ): Promise<boolean> {
    try {
      const { headers } = await githubAppAuth.createAPIClient(installationId);
      
      const response = await fetch(
        `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/comments`,
        {
          method: 'POST',
          headers: {
            ...headers,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            body,
            path: filePath,
            line,
            side: 'RIGHT',
            commit_id: sha
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`Failed to post review comment: ${response.status}`, errorData);
        return false;
      }

      console.log(`Review comment posted successfully for ${owner}/${repo}#${prNumber} on ${filePath}:${line}`);
      return true;
    } catch (error) {
      console.error('Error posting review comment:', error);
      return false;
    }
  }

  /**
   * Format security review summary as markdown
   */
  formatSecuritySummary(summary: SecurityReviewSummary, prInfo: PRInfo): string {
    const { total_findings, critical_findings, high_findings, success, analyzers_run } = summary;
    
    let emoji = '✅';
    let status = 'No security issues found';
    
    if (!success) {
      emoji = '❌';
      status = 'Analysis failed';
    } else if (critical_findings > 0) {
      emoji = '🚨';
      status = `${critical_findings} critical security issue${critical_findings === 1 ? '' : 's'} found`;
    } else if (high_findings > 0) {
      emoji = '⚠️';
      status = `${high_findings} high-severity security issue${high_findings === 1 ? '' : 's'} found`;
    } else if (total_findings > 0) {
      emoji = '⚡';
      status = `${total_findings} security issue${total_findings === 1 ? '' : 's'} found`;
    }

    return `## ${emoji} Platyfend Security Review

**${status}**

### Summary
- **Total findings:** ${total_findings}
- **Critical:** ${critical_findings}
- **High severity:** ${high_findings}
- **Analyzers run:** ${analyzers_run}
- **Status:** ${success ? 'Completed' : 'Failed'}

### Pull Request Details
- **Title:** ${prInfo.title}
- **Author:** ${prInfo.author}
- **Branch:** \`${prInfo.head_branch}\` → \`${prInfo.base_branch}\`

${total_findings > 0 ? '### 📋 Review the detailed findings in the file comments below.' : ''}

---
*Powered by [Platyfend](${process.env.NEXT_PUBLIC_APP_URL}) - Next generation secure code review*`;
  }

  /**
   * Format individual security finding as markdown
   */
  formatSecurityFinding(comment: GitHubComment): string {
    const severityEmoji = this.getSeverityEmoji(comment.severity);
    const severityText = comment.severity ? ` **${comment.severity.toUpperCase()}**` : '';
    
    return `${severityEmoji}${severityText} Security Finding

${comment.body}

${comment.finding_ids && comment.finding_ids.length > 0 ? `\n*Finding IDs: ${comment.finding_ids.join(', ')}*` : ''}

---
*Detected by Platyfend Security Analysis*`;
  }

  /**
   * Get emoji for severity level
   */
  private getSeverityEmoji(severity?: string): string {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return '🚨';
      case 'high':
        return '⚠️';
      case 'medium':
        return '⚡';
      case 'low':
        return 'ℹ️';
      default:
        return '🔍';
    }
  }

  /**
   * Process security review and post comments to GitHub PR
   */
  async processSecurityReview(
    prInfo: PRInfo,
    summary: SecurityReviewSummary,
    comments: GitHubComment[],
    sha?: string
  ): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // Parse repository owner and name
      const [owner, repo] = prInfo.repository.split('/');
      if (!owner || !repo) {
        throw new Error(`Invalid repository format: ${prInfo.repository}`);
      }

      // Get installation ID for the repository
      const installationId = await githubStatusService.getInstallationIdForRepo(owner, repo);
      if (!installationId) {
        throw new Error(`No GitHub installation found for repository ${prInfo.repository}`);
      }

      // Get commit SHA if not provided
      let commitSHA = sha;
      if (!commitSHA) {
        const fetchedSHA = await this.getPRCommitSHA(installationId, owner, repo, prInfo.number);
        if (!fetchedSHA) {
          console.warn(`Could not retrieve commit SHA for PR #${prInfo.number}. Review comments may not work properly.`);
          commitSHA = 'HEAD'; // Fallback
        } else {
          commitSHA = fetchedSHA;
        }
      }

      // Post summary comment
      const summaryBody = this.formatSecuritySummary(summary, prInfo);
      const summarySuccess = await this.postPRComment(
        installationId,
        owner,
        repo,
        prInfo.number,
        summaryBody
      );

      if (!summarySuccess) {
        errors.push('Failed to post security review summary comment');
      }

      // Post individual file comments
      for (const comment of comments) {
        if (comment.file_path && comment.line) {
          // Post as review comment on specific line
          const formattedBody = this.formatSecurityFinding(comment);
          const commentSuccess = await this.postReviewComment(
            installationId,
            owner,
            repo,
            prInfo.number,
            formattedBody,
            comment.file_path,
            comment.line,
            commitSHA
          );

          if (!commentSuccess) {
            errors.push(`Failed to post review comment on ${comment.file_path}:${comment.line}`);
          }
        } else {
          // Post as general PR comment if no file/line specified
          const formattedBody = this.formatSecurityFinding(comment);
          const commentSuccess = await this.postPRComment(
            installationId,
            owner,
            repo,
            prInfo.number,
            formattedBody
          );

          if (!commentSuccess) {
            errors.push('Failed to post general security finding comment');
          }
        }
      }

      // Update GitHub status based on results
      if (summary.success) {
        await githubStatusService.setSuccessStatus(
          installationId,
          owner,
          repo,
          commitSHA,
          prInfo.number,
          summary.total_findings
        );
      } else {
        await githubStatusService.setFailureStatus(
          installationId,
          owner,
          repo,
          commitSHA,
          prInfo.number,
          'Security analysis failed'
        );
      }

      return {
        success: errors.length === 0,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error processing security review:', errorMessage);
      errors.push(errorMessage);

      return {
        success: false,
        errors
      };
    }
  }
}

// Singleton instance
export const githubCommentService = new GitHubCommentService();

// Helper function for easy access
export async function postSecurityReview(
  prInfo: PRInfo,
  summary: SecurityReviewSummary,
  comments: GitHubComment[],
  sha?: string
): Promise<{ success: boolean; errors: string[] }> {
  return githubCommentService.processSecurityReview(prInfo, summary, comments, sha);
}
