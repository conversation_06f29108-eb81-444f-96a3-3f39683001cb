/**
 * Error Reporting Service
 * Handles error reporting to various monitoring and alerting systems
 */

import { PlatyfendError } from '../error-handling';
import { ErrorFormatter, ErrorClassifier } from './utils';

/**
 * Monitoring service configuration
 */
export interface MonitoringConfig {
  enabled: boolean;
  service: 'sentry' | 'datadog' | 'newrelic' | 'custom';
  apiKey?: string;
  endpoint?: string;
  environment: string;
  release?: string;
  tags?: Record<string, string>;
}

/**
 * Notification channel configuration
 */
export interface NotificationChannel {
  type: 'slack' | 'email' | 'webhook' | 'sms';
  enabled: boolean;
  config: Record<string, unknown>;
  filters?: {
    severity?: ('low' | 'medium' | 'high' | 'critical')[];
    types?: string[];
    components?: string[];
  };
}

/**
 * Error reporting configuration
 */
export interface ErrorReportingConfig {
  monitoring: MonitoringConfig;
  notifications: NotificationChannel[];
  sampling: {
    enabled: boolean;
    rate: number; // 0-1, percentage of errors to report
    criticalAlways: boolean; // Always report critical errors regardless of sampling
  };
  privacy: {
    sanitizeUserData: boolean;
    excludeFields: string[];
    hashSensitiveData: boolean;
  };
}

/**
 * Error Reporter Service
 */
export class ErrorReporter {
  private config: ErrorReportingConfig;
  private reportQueue: PlatyfendError[] = [];
  private isProcessing = false;

  constructor(config: ErrorReportingConfig) {
    this.config = config;
    
    // Start processing queue
    this.startQueueProcessor();
  }

  /**
   * Report error to all configured services
   */
  async reportError(error: PlatyfendError): Promise<void> {
    // Apply sampling
    if (!this.shouldReport(error)) {
      return;
    }

    // Sanitize error data
    const sanitizedError = this.sanitizeError(error);

    // Add to queue for processing
    this.reportQueue.push(sanitizedError);
  }

  /**
   * Report multiple errors in batch
   */
  async reportErrors(errors: PlatyfendError[]): Promise<void> {
    for (const error of errors) {
      await this.reportError(error);
    }
  }

  /**
   * Check if error should be reported based on sampling and filters
   */
  private shouldReport(error: PlatyfendError): boolean {
    // Always report critical errors
    if (error.severity === 'critical' && this.config.sampling.criticalAlways) {
      return true;
    }

    // Apply sampling rate
    if (this.config.sampling.enabled) {
      return Math.random() < this.config.sampling.rate;
    }

    return true;
  }

  /**
   * Sanitize error data for privacy
   */
  private sanitizeError(error: PlatyfendError): PlatyfendError {
    if (!this.config.privacy.sanitizeUserData) {
      return error;
    }

    const sanitized = { ...error };

    // Remove excluded fields
    for (const field of this.config.privacy.excludeFields) {
      this.removeField(sanitized, field);
    }

    // Hash sensitive data
    if (this.config.privacy.hashSensitiveData) {
      sanitized.context = this.hashSensitiveFields(sanitized.context);
    }

    return sanitized;
  }

  /**
   * Remove field from object recursively
   */
  private removeField(obj: any, fieldPath: string): void {
    const parts = fieldPath.split('.');
    let current = obj;

    for (let i = 0; i < parts.length - 1; i++) {
      if (current[parts[i]]) {
        current = current[parts[i]];
      } else {
        return;
      }
    }

    delete current[parts[parts.length - 1]];
  }

  /**
   * Hash sensitive fields
   */
  private hashSensitiveFields(obj: any): any {
    const sensitiveFields = ['email', 'userId', 'sessionId', 'token'];
    const result = { ...obj };

    for (const field of sensitiveFields) {
      if (result[field]) {
        result[field] = this.hashValue(result[field]);
      }
    }

    return result;
  }

  /**
   * Simple hash function for sensitive data
   */
  private hashValue(value: string): string {
    // In production, use a proper hashing library
    return `hash_${value.length}_${value.charCodeAt(0)}`;
  }

  /**
   * Start queue processor
   */
  private startQueueProcessor(): void {
    setInterval(async () => {
      if (this.isProcessing || this.reportQueue.length === 0) {
        return;
      }

      this.isProcessing = true;
      
      try {
        const errors = this.reportQueue.splice(0, 10); // Process up to 10 errors at a time
        await this.processErrorBatch(errors);
      } catch (error) {
        console.error('Error processing report queue:', error);
      } finally {
        this.isProcessing = false;
      }
    }, 5000); // Process every 5 seconds
  }

  /**
   * Process batch of errors
   */
  private async processErrorBatch(errors: PlatyfendError[]): Promise<void> {
    const promises: Promise<void>[] = [];

    // Send to monitoring service
    if (this.config.monitoring.enabled) {
      promises.push(this.sendToMonitoring(errors));
    }

    // Send notifications
    for (const error of errors) {
      if (ErrorClassifier.shouldAlert(error)) {
        promises.push(this.sendNotifications(error));
      }
    }

    await Promise.allSettled(promises);
  }

  /**
   * Send errors to monitoring service
   */
  private async sendToMonitoring(errors: PlatyfendError[]): Promise<void> {
    try {
      switch (this.config.monitoring.service) {
        case 'sentry':
          await this.sendToSentry(errors);
          break;
        case 'datadog':
          await this.sendToDatadog(errors);
          break;
        case 'newrelic':
          await this.sendToNewRelic(errors);
          break;
        case 'custom':
          await this.sendToCustomEndpoint(errors);
          break;
      }
    } catch (error) {
      console.error('Failed to send to monitoring service:', error);
    }
  }

  /**
   * Send to Sentry
   */
  private async sendToSentry(errors: PlatyfendError[]): Promise<void> {
    // Implementation would use Sentry SDK
    console.log('Sending to Sentry:', errors.length, 'errors');
  }

  /**
   * Send to Datadog
   */
  private async sendToDatadog(errors: PlatyfendError[]): Promise<void> {
    // Implementation would use Datadog API
    console.log('Sending to Datadog:', errors.length, 'errors');
  }

  /**
   * Send to New Relic
   */
  private async sendToNewRelic(errors: PlatyfendError[]): Promise<void> {
    // Implementation would use New Relic API
    console.log('Sending to New Relic:', errors.length, 'errors');
  }

  /**
   * Send to custom endpoint
   */
  private async sendToCustomEndpoint(errors: PlatyfendError[]): Promise<void> {
    if (!this.config.monitoring.endpoint) {
      return;
    }

    const payload = {
      errors: errors.map(error => ErrorFormatter.formatForMonitoring(error)),
      timestamp: new Date().toISOString(),
      environment: this.config.monitoring.environment,
      release: this.config.monitoring.release,
    };

    const endpoint = this.config.monitoring.endpoint;

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.monitoring.apiKey}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        let errorDetails = '';
        try {
          // Try to read response as JSON first, fallback to text
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();
            errorDetails = JSON.stringify(errorData);
          } else {
            errorDetails = await response.text();
          }
        } catch (parseError) {
          errorDetails = `Failed to parse error response: ${parseError}`;
        }

        console.error('Custom monitoring endpoint returned non-OK status:', {
          endpoint,
          status: response.status,
          statusText: response.statusText,
          errorDetails,
          requestPayload: payload,
        });

        throw new Error(
          `Custom monitoring endpoint failed with status ${response.status}: ${response.statusText}. Details: ${errorDetails}`
        );
      }

      // Log successful submission for debugging
      console.log('Successfully sent errors to custom monitoring endpoint:', {
        endpoint,
        status: response.status,
        errorCount: errors.length,
      });

    } catch (error) {
      // Handle network errors, timeouts, and other fetch failures
      console.error('Failed to send errors to custom monitoring endpoint:', {
        endpoint,
        error: error instanceof Error ? error.message : String(error),
        errorCount: errors.length,
        requestPayload: payload,
      });

      // Re-throw the error so callers can handle the failure appropriately
      throw new Error(
        `Custom monitoring endpoint request failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Send notifications for error
   */
  private async sendNotifications(error: PlatyfendError): Promise<void> {
    const promises = this.config.notifications
      .filter(channel => this.shouldNotify(channel, error))
      .map(channel => this.sendNotification(channel, error));

    await Promise.allSettled(promises);
  }

  /**
   * Check if channel should receive notification for error
   */
  private shouldNotify(channel: NotificationChannel, error: PlatyfendError): boolean {
    if (!channel.enabled) return false;

    const filters = channel.filters;
    if (!filters) return true;

    // Check severity filter
    if (filters.severity && !filters.severity.includes(error.severity)) {
      return false;
    }

    // Check type filter
    if (filters.types && !filters.types.includes(error.type)) {
      return false;
    }

    // Check component filter
    if (filters.components && !filters.components.includes(error.component)) {
      return false;
    }

    return true;
  }

  /**
   * Send notification to specific channel
   */
  private async sendNotification(channel: NotificationChannel, error: PlatyfendError): Promise<void> {
    try {
      switch (channel.type) {
        case 'slack':
          await this.sendSlackNotification(channel.config, error);
          break;
        case 'email':
          await this.sendEmailNotification(channel.config, error);
          break;
        case 'webhook':
          await this.sendWebhookNotification(channel.config, error);
          break;
        case 'sms':
          await this.sendSMSNotification(channel.config, error);
          break;
      }
    } catch (notificationError) {
      console.error(`Failed to send ${channel.type} notification:`, notificationError);
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(config: Record<string, unknown>, error: PlatyfendError): Promise<void> {
    const formatted = ErrorFormatter.formatForUser(error);
    
    const payload = {
      text: `🚨 ${formatted.title}: ${formatted.message}`,
      attachments: [
        {
          color: error.severity === 'critical' ? 'danger' : 'warning',
          fields: [
            { title: 'Component', value: error.component, short: true },
            { title: 'Type', value: error.type, short: true },
            { title: 'Error ID', value: error.id, short: true },
            { title: 'Timestamp', value: error.context.timestamp.toISOString(), short: true },
          ],
        },
      ],
    };

    // Implementation would send to Slack webhook
    console.log('Slack notification:', payload);
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(config: Record<string, unknown>, error: PlatyfendError): Promise<void> {
    // Implementation would send email
    console.log('Email notification for error:', error.id);
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(config: Record<string, unknown>, error: PlatyfendError): Promise<void> {
    // Implementation would send to webhook
    console.log('Webhook notification for error:', error.id);
  }

  /**
   * Send SMS notification
   */
  private async sendSMSNotification(config: Record<string, unknown>, error: PlatyfendError): Promise<void> {
    // Implementation would send SMS
    console.log('SMS notification for error:', error.id);
  }

  /**
   * Get queue status
   */
  getQueueStatus(): { pending: number; processing: boolean } {
    return {
      pending: this.reportQueue.length,
      processing: this.isProcessing,
    };
  }

  /**
   * Flush queue immediately
   */
  async flushQueue(): Promise<void> {
    if (this.reportQueue.length === 0) return;

    const errors = this.reportQueue.splice(0);
    await this.processErrorBatch(errors);
  }
}
