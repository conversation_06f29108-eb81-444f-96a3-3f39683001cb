/**
 * Error Handling Module Index
 * Exports all error handling utilities and services
 */

// Core error handling
export {
  ErrorHandlingService,
  ErrorType,
  type ErrorContext,
  type RecoveryAction,
  type ErrorDetails,
  type PlatyfendError,
  errorHandlingService,
  createErrorContext,
  isRetryableError
} from '../error-handling';

// Import for extending
import {
  ErrorHandlingService,
  ErrorType,
  type ErrorContext,
  type PlatyfendError
} from '../error-handling';

// Error utilities
export {
  ErrorClassifier,
  ErrorRecovery,
  ErrorFormatter
} from './utils';

// Error reporting
export {
  ErrorReporter,
  type MonitoringConfig,
  type NotificationChannel,
  type ErrorReportingConfig
} from './reporter';

// Create default error reporter instance
import { ErrorReporter, ErrorReportingConfig } from './reporter';

const defaultReportingConfig: ErrorReportingConfig = {
  monitoring: {
    enabled: process.env.NODE_ENV === 'production',
    service: 'custom',
    endpoint: process.env.ERROR_REPORTING_ENDPOINT,
    apiKey: process.env.ERROR_REPORTING_API_KEY,
    environment: process.env.NODE_ENV || 'development',
    release: process.env.APP_VERSION,
    tags: {
      service: 'platyfend-dashboard',
      component: 'frontend'
    }
  },
  notifications: [
    {
      type: 'slack',
      enabled: !!process.env.SLACK_WEBHOOK_URL,
      config: {
        webhookUrl: process.env.SLACK_WEBHOOK_URL,
        channel: '#alerts'
      },
      filters: {
        severity: ['high', 'critical']
      }
    }
  ],
  sampling: {
    enabled: true,
    rate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 10% in prod, 100% in dev
    criticalAlways: true
  },
  privacy: {
    sanitizeUserData: true,
    excludeFields: ['context.userAgent', 'details.headers.authorization'],
    hashSensitiveData: true
  }
};

export const errorReporter = new ErrorReporter(defaultReportingConfig);

// Enhanced error handling service that integrates with reporter
export class EnhancedErrorHandlingService extends ErrorHandlingService {
  constructor(private reporter: ErrorReporter) {
    super();
  }

  /**
   * Enhanced error logging with reporting
   */
  async logError(error: PlatyfendError): Promise<void> {
    // Call parent logging
    super.logError(error);
    
    // Report to monitoring services
    await this.reporter.reportError(error);
  }

  /**
   * Handle error with automatic reporting
   */
  async handleAndReport(
    error: unknown,
    context: ErrorContext,
    errorType: ErrorType = ErrorType.UNKNOWN_ERROR
  ): Promise<PlatyfendError> {
    let platyfendError: PlatyfendError;

    // Handle different error types
    switch (errorType) {
      case ErrorType.GITHUB_API_ERROR:
        platyfendError = this.handleGitHubAPIError(error, context);
        break;
      case ErrorType.INSTALLATION_ERROR:
        platyfendError = this.handleInstallationError(error, context);
        break;
      case ErrorType.WEBHOOK_ERROR:
        platyfendError = this.handleWebhookError(error, context);
        break;
      case ErrorType.SYNC_ERROR:
        platyfendError = this.handleSyncError(error, context);
        break;
      case ErrorType.DATABASE_ERROR:
        platyfendError = this.handleDatabaseError(error, context);
        break;
      default:
        platyfendError = this.handleGenericError(error, context);
    }

    // Log and report
    await this.logError(platyfendError);

    return platyfendError;
  }

  /**
   * Handle generic errors
   */
  private handleGenericError(error: unknown, context: ErrorContext): PlatyfendError {
    const timestamp = new Date();
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return this.createBaseError(
      ErrorType.UNKNOWN_ERROR,
      errorMessage,
      'An unexpected error occurred. Please try again.',
      { ...context, timestamp },
      error instanceof Error ? error : undefined
    );
  }
}

// Create enhanced service instance
export const enhancedErrorHandlingService = new EnhancedErrorHandlingService(errorReporter);

// Convenience functions
export async function reportError(error: PlatyfendError): Promise<void> {
  return errorReporter.reportError(error);
}

export async function handleError(
  error: unknown,
  context: ErrorContext,
  errorType?: ErrorType
): Promise<PlatyfendError> {
  return enhancedErrorHandlingService.handleAndReport(error, context, errorType);
}

export function createEnhancedErrorContext(
  operation: string,
  additionalContext: Partial<ErrorContext> = {}
): ErrorContext {
  return {
    operation,
    timestamp: new Date(),
    environment: process.env.NODE_ENV as any || 'development',
    version: process.env.APP_VERSION,
    buildId: process.env.BUILD_ID,
    requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    ...additionalContext
  };
}
