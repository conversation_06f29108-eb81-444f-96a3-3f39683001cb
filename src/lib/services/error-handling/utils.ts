/**
 * Error Handling Utilities
 * Helper functions for error processing, classification, and recovery
 */

import { ErrorType, PlatyfendError, ErrorContext, RecoveryAction } from '../error-handling';

/**
 * Error classification utilities
 */
export class ErrorClassifier {
  /**
   * Classify error by HTTP status code
   */
  static classifyByStatusCode(statusCode: number): {
    type: ErrorType;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'user' | 'system' | 'external' | 'configuration';
    retryable: boolean;
  } {
    if (statusCode >= 200 && statusCode < 300) {
      return {
        type: ErrorType.UNKNOWN_ERROR,
        severity: 'low',
        category: 'system',
        retryable: false
      };
    }

    if (statusCode >= 400 && statusCode < 500) {
      const clientErrorMap: Record<number, any> = {
        400: { type: ErrorType.VALIDATION_ERROR, severity: 'medium', retryable: false },
        401: { type: ErrorType.AUTH_ERROR, severity: 'high', retryable: false },
        403: { type: ErrorType.PERMISSION_ERROR, severity: 'medium', retryable: false },
        404: { type: ErrorType.EXTERNAL_API_ERROR, severity: 'low', retryable: false },
        409: { type: ErrorType.BUSINESS_RULE_ERROR, severity: 'medium', retryable: false },
        422: { type: ErrorType.VALIDATION_ERROR, severity: 'medium', retryable: false },
        429: { type: ErrorType.RATE_LIMIT_ERROR, severity: 'medium', retryable: true },
      };

      const errorInfo = clientErrorMap[statusCode] || {
        type: ErrorType.EXTERNAL_API_ERROR,
        severity: 'medium',
        retryable: false
      };

      return {
        ...errorInfo,
        category: 'user'
      };
    }

    if (statusCode >= 500) {
      return {
        type: ErrorType.EXTERNAL_API_ERROR,
        severity: statusCode >= 503 ? 'high' : 'medium',
        category: 'external',
        retryable: true
      };
    }

    return {
      type: ErrorType.UNKNOWN_ERROR,
      severity: 'medium',
      category: 'system',
      retryable: false
    };
  }

  /**
   * Classify error by error message patterns
   */
  static classifyByMessage(message: string): {
    type: ErrorType;
    keywords: string[];
    confidence: number;
  } {
    const patterns = [
      {
        type: ErrorType.NETWORK_ERROR,
        keywords: ['network', 'connection', 'timeout', 'ECONNRESET', 'ETIMEDOUT'],
        confidence: 0.9
      },
      {
        type: ErrorType.DATABASE_ERROR,
        keywords: ['database', 'mongo', 'sql', 'connection pool', 'transaction'],
        confidence: 0.85
      },
      {
        type: ErrorType.AUTH_ERROR,
        keywords: ['authentication', 'unauthorized', 'token', 'login', 'session'],
        confidence: 0.8
      },
      {
        type: ErrorType.VALIDATION_ERROR,
        keywords: ['validation', 'invalid', 'required', 'format', 'schema'],
        confidence: 0.75
      },
      {
        type: ErrorType.PERMISSION_ERROR,
        keywords: ['permission', 'forbidden', 'access denied', 'not allowed'],
        confidence: 0.8
      }
    ];

    const lowerMessage = message.toLowerCase();
    
    for (const pattern of patterns) {
      const matchedKeywords = pattern.keywords.filter(keyword => 
        lowerMessage.includes(keyword.toLowerCase())
      );
      
      if (matchedKeywords.length > 0) {
        return {
          type: pattern.type,
          keywords: matchedKeywords,
          confidence: pattern.confidence * (matchedKeywords.length / pattern.keywords.length)
        };
      }
    }

    return {
      type: ErrorType.UNKNOWN_ERROR,
      keywords: [],
      confidence: 0
    };
  }

  /**
   * Determine if error is user-facing
   */
  static isUserFacing(error: PlatyfendError): boolean {
    const userFacingTypes = [
      ErrorType.VALIDATION_ERROR,
      ErrorType.PERMISSION_ERROR,
      ErrorType.AUTH_ERROR,
      ErrorType.BUSINESS_RULE_ERROR,
      ErrorType.RATE_LIMIT_ERROR
    ];

    return userFacingTypes.includes(error.type) || error.category === 'user';
  }

  /**
   * Determine if error should trigger alerts
   */
  static shouldAlert(error: PlatyfendError): boolean {
    // Always alert for critical errors
    if (error.severity === 'critical') return true;
    
    // Alert for high severity errors in production
    if (error.severity === 'high' && process.env.NODE_ENV === 'production') return true;
    
    // Alert for repeated errors
    if (error.metrics.errorCount > 5) return true;
    
    // Alert for errors affecting multiple users
    if (error.metrics.affectedUsers > 1) return true;
    
    return false;
  }
}

/**
 * Error recovery utilities
 */
export class ErrorRecovery {
  /**
   * Generate recovery actions based on error type
   */
  static generateRecoveryActions(error: PlatyfendError): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
      case ErrorType.TIMEOUT_ERROR:
        actions.push({
          type: 'retry',
          description: 'Retry the operation',
          automated: true,
          priority: 'high',
          estimatedTime: '30 seconds'
        });
        break;

      case ErrorType.AUTH_ERROR:
      case ErrorType.TOKEN_ERROR:
        actions.push({
          type: 'refresh_token',
          description: 'Refresh authentication token',
          automated: true,
          priority: 'high',
          estimatedTime: '10 seconds'
        });
        break;

      case ErrorType.RATE_LIMIT_ERROR:
        actions.push({
          type: 'wait_and_retry',
          description: 'Wait for rate limit to reset and retry',
          automated: true,
          priority: 'medium',
          estimatedTime: '1-5 minutes'
        });
        break;

      case ErrorType.DATABASE_ERROR:
        actions.push({
          type: 'retry',
          description: 'Retry database operation',
          automated: true,
          priority: 'medium',
          estimatedTime: '30 seconds'
        });
        break;

      case ErrorType.VALIDATION_ERROR:
        actions.push({
          type: 'contact_support',
          description: 'Contact support for validation error resolution',
          automated: false,
          priority: 'low',
          estimatedTime: '1-2 hours'
        });
        break;

      default:
        actions.push({
          type: 'contact_support',
          description: 'Contact support for assistance',
          automated: false,
          priority: 'medium',
          estimatedTime: '30 minutes - 2 hours'
        });
    }

    return actions;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  static calculateRetryDelay(attempt: number, baseDelay: number = 1000, maxDelay: number = 30000): number {
    const delay = baseDelay * Math.pow(2, attempt - 1);
    return Math.min(delay, maxDelay);
  }

  /**
   * Determine if error should be retried
   */
  static shouldRetry(error: PlatyfendError, attempt: number, maxAttempts: number = 3): boolean {
    if (attempt >= maxAttempts) return false;
    if (!error.retryable) return false;
    
    // Don't retry user errors
    if (error.category === 'user') return false;
    
    // Don't retry validation errors
    if (error.type === ErrorType.VALIDATION_ERROR) return false;
    
    return true;
  }
}

/**
 * Error formatting utilities
 */
export class ErrorFormatter {
  /**
   * Format error for user display
   */
  static formatForUser(error: PlatyfendError): {
    title: string;
    message: string;
    actions: string[];
    severity: string;
  } {
    const severityMap = {
      low: 'Info',
      medium: 'Warning',
      high: 'Error',
      critical: 'Critical Error'
    };

    return {
      title: severityMap[error.severity],
      message: error.userMessage,
      actions: error.recoveryActions
        .filter(action => !action.automated)
        .map(action => action.description),
      severity: error.severity
    };
  }

  /**
   * Format error for logging
   */
  static formatForLogging(error: PlatyfendError): {
    level: string;
    message: string;
    metadata: Record<string, unknown>;
  } {
    const levelMap = {
      low: 'info',
      medium: 'warn',
      high: 'error',
      critical: 'error'
    };

    return {
      level: levelMap[error.severity],
      message: `[${error.type}] ${error.message}`,
      metadata: {
        errorId: error.id,
        type: error.type,
        category: error.category,
        component: error.component,
        context: error.context,
        details: error.details,
        metrics: error.metrics
      }
    };
  }

  /**
   * Format error for monitoring/alerting
   */
  static formatForMonitoring(error: PlatyfendError): {
    alert: boolean;
    level: string;
    title: string;
    description: string;
    tags: string[];
    metadata: Record<string, unknown>;
  } {
    return {
      alert: ErrorClassifier.shouldAlert(error),
      level: error.alerting.alertLevel,
      title: `${error.type}: ${error.component}`,
      description: error.message,
      tags: [
        error.type,
        error.category,
        error.severity,
        error.component,
        ...(error.context.tags || [])
      ],
      metadata: {
        errorId: error.id,
        context: error.context,
        metrics: error.metrics,
        recoveryActions: error.recoveryActions.length
      }
    };
  }
}
