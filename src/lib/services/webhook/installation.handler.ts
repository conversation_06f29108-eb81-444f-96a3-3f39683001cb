/**
 * Installation Webhook Handler
 * Handles GitHub App installation webhook events
 */

import { z } from 'zod';
import { WebhookSyncResult } from '@/src/lib/types/webhooks';
import { webhookValidationService, InstallationPayloadSchema } from './validation.service';
import { webhookMetricsService } from './metrics.service';
import { InstallationHandlers } from '../installation-handlers';
import { RepositoryHandlers } from '../repository-handlers';
import { errorHandlingService, createErrorContext } from '../error-handling';

/**
 * Installation processing configuration
 */
interface InstallationProcessingConfig {
  enableRepositorySync: boolean;
  enableStatusUpdates: boolean;
}

const DEFAULT_INSTALLATION_CONFIG: InstallationProcessingConfig = {
  enableRepositorySync: true,
  enableStatusUpdates: true,
};

/**
 * Installation Webhook Handler
 */
export class InstallationWebhookHandler {
  private installationHandlers = new InstallationHandlers();
  private repositoryHandlers = new RepositoryHandlers();
  private config: InstallationProcessingConfig;

  constructor(config: Partial<InstallationProcessingConfig> = {}) {
    this.config = { ...DEFAULT_INSTALLATION_CONFIG, ...config };
  }

  /**
   * Handle installation webhook event
   */
  async handleInstallationEvent(payload: unknown): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const result: WebhookSyncResult = {
      success: false,
      action: 'installation.unknown',
      repositoriesAffected: 0,
      errors: [],
    };

    try {
      // Extract installation ID for validation
      const installationId = this.extractInstallationId(payload);
      
      // Validate payload
      const validation = webhookValidationService.validateInstallationPayload(payload, installationId);

      if (!validation.isValid) {
        result.errors = validation.errors;
        if (validation.rateLimited) {
          result.action = 'installation.rate_limited';
        }
        this.recordMetrics(result, startTime, installationId);
        return result;
      }

      const validatedPayload = validation.data!;
      result.action = `installation.${validatedPayload.action}`;

      // Process based on action type
      switch (validatedPayload.action) {
        case 'created':
          await this.handleInstallationCreated(validatedPayload, result);
          break;
        case 'deleted':
          await this.handleInstallationDeleted(validatedPayload, result);
          break;
        case 'suspend':
          await this.handleInstallationSuspended(validatedPayload, result);
          break;
        case 'unsuspend':
          await this.handleInstallationUnsuspended(validatedPayload, result);
          break;
        case 'new_permissions_accepted':
          await this.handlePermissionsAccepted(validatedPayload, result);
          break;
        default:
          result.errors.push(`Unsupported installation action: ${validatedPayload.action}`);
      }

      result.success = result.errors.length === 0;
      this.recordMetrics(result, startTime, installationId);
      return result;

    } catch (error) {
      return this.handleError(error, payload, startTime, result);
    }
  }

  /**
   * Handle installation repositories webhook event
   */
  async handleInstallationRepositoriesEvent(payload: unknown): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const result: WebhookSyncResult = {
      success: false,
      action: 'installation_repositories.unknown',
      repositoriesAffected: 0,
      errors: [],
    };

    try {
      // Extract installation ID for validation
      const installationId = this.extractInstallationId(payload);
      
      // Validate payload
      const validation = webhookValidationService.validateInstallationRepositoriesPayload(payload, installationId);

      if (!validation.isValid) {
        result.errors = validation.errors;
        if (validation.rateLimited) {
          result.action = 'installation_repositories.rate_limited';
        }
        this.recordMetrics(result, startTime, installationId);
        return result;
      }

      const payloadData = validation.data as any;
      result.action = `installation_repositories.${payloadData.action}`;

      // Process repository changes
      if (payloadData.action === 'added') {
        await this.handleRepositoriesAdded(payloadData, result);
      } else if (payloadData.action === 'removed') {
        await this.handleRepositoriesRemoved(payloadData, result);
      } else {
        result.errors.push(`Unsupported installation repositories action: ${payloadData.action}`);
      }

      result.success = result.errors.length === 0;
      this.recordMetrics(result, startTime, installationId);
      return result;

    } catch (error) {
      return this.handleError(error, payload, startTime, result);
    }
  }

  /**
   * Handle installation created event
   */
  private async handleInstallationCreated(
    payload: z.infer<typeof InstallationPayloadSchema>,
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      const installationId = payload.installation.id.toString();
      const accountLogin = payload.installation.account.login;

      console.log(`🔧 Processing installation created for ${accountLogin} (ID: ${installationId})`);

      // Create or update organization record
      await this.installationHandlers.handleInstallationCreated(
        installationId,
        payload.installation,
        payload.repositories,
        payload.repository_selection,
        result
      );

      // Sync repositories if provided
      if (this.config.enableRepositorySync && payload.repositories) {
        result.repositoriesAffected = payload.repositories.length;
        console.log(`📦 Syncing ${payload.repositories.length} repositories for installation ${installationId}`);
      }

      result.organizationId = payload.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process installation created: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle installation deleted event
   */
  private async handleInstallationDeleted(
    payload: z.infer<typeof InstallationPayloadSchema>,
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      const installationId = payload.installation.id.toString();
      const accountLogin = payload.installation.account.login;

      console.log(`🗑️ Processing installation deleted for ${accountLogin} (ID: ${installationId})`);

      // Mark installation as deleted
      await this.installationHandlers.handleInstallationDeleted(installationId, payload.installation, result);

      result.organizationId = payload.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process installation deleted: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle installation suspended event
   */
  private async handleInstallationSuspended(
    payload: z.infer<typeof InstallationPayloadSchema>,
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      const installationId = payload.installation.id.toString();
      console.log(`⏸️ Processing installation suspended (ID: ${installationId})`);

      await this.installationHandlers.handleInstallationSuspended(installationId, payload.installation, result);
      result.organizationId = payload.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process installation suspended: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle installation unsuspended event
   */
  private async handleInstallationUnsuspended(
    payload: z.infer<typeof InstallationPayloadSchema>,
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      const installationId = payload.installation.id.toString();
      console.log(`▶️ Processing installation unsuspended (ID: ${installationId})`);

      await this.installationHandlers.handleInstallationUnsuspended(installationId, payload.installation, result);
      result.organizationId = payload.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process installation unsuspended: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle new permissions accepted event
   */
  private async handlePermissionsAccepted(
    payload: z.infer<typeof InstallationPayloadSchema>,
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      const installationId = payload.installation.id.toString();
      console.log(`✅ Processing new permissions accepted (ID: ${installationId})`);

      // Update installation permissions
      await this.installationHandlers.handlePermissionsUpdated(installationId, payload.installation);
      result.organizationId = payload.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process permissions accepted: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repositories added to installation
   */
  private async handleRepositoriesAdded(payloadData: any, result: WebhookSyncResult): Promise<void> {
    try {
      const installationId = payloadData.installation.id.toString();
      const repositories = payloadData.repositories_added || [];

      console.log(`➕ Processing ${repositories.length} repositories added to installation ${installationId}`);

      if (this.config.enableRepositorySync) {
        await this.repositoryHandlers.handleRepositoriesAdded(installationId, repositories);
        result.repositoriesAffected = repositories.length;
      }

      result.organizationId = payloadData.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process repositories added: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repositories removed from installation
   */
  private async handleRepositoriesRemoved(payloadData: any, result: WebhookSyncResult): Promise<void> {
    try {
      const installationId = payloadData.installation.id.toString();
      const repositories = payloadData.repositories_removed || [];

      console.log(`➖ Processing ${repositories.length} repositories removed from installation ${installationId}`);

      if (this.config.enableRepositorySync) {
        await this.repositoryHandlers.handleRepositoriesRemoved(installationId, repositories);
        result.repositoriesAffected = repositories.length;
      }

      result.organizationId = payloadData.installation.account.id.toString();

    } catch (error) {
      result.errors.push(`Failed to process repositories removed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract installation ID from payload
   */
  private extractInstallationId(payload: unknown): string | undefined {
    if (payload && typeof payload === 'object' && 'installation' in payload) {
      const installation = (payload as any).installation;
      return installation?.id?.toString();
    }
    return undefined;
  }

  /**
   * Record metrics for the webhook processing
   */
  private recordMetrics(result: WebhookSyncResult, startTime: number, installationId?: string): void {
    const duration = Date.now() - startTime;
    webhookMetricsService.recordMetric({
      action: result.action,
      success: result.success,
      duration,
      repositoriesAffected: result.repositoriesAffected,
      organizationId: result.organizationId,
      installationId,
      errorType: result.errors.length > 0 ? 'processing_error' : undefined,
    });
  }

  /**
   * Handle errors during processing
   */
  private handleError(
    error: unknown,
    payload: unknown,
    startTime: number,
    result: WebhookSyncResult
  ): WebhookSyncResult {
    const duration = Date.now() - startTime;
    const installationId = this.extractInstallationId(payload);

    const context = createErrorContext(
      undefined, // userId
      undefined, // organizationId
      installationId,
      undefined, // repositoryId
      'installation_webhook'
    );

    const platyfendError = errorHandlingService.handleWebhookError(error, context);
    errorHandlingService.logError(platyfendError);

    result.success = false;
    result.errors.push(platyfendError.message);
    result.metrics = {
      duration,
      timestamp: new Date(),
    };

    webhookMetricsService.recordMetric({
      action: result.action,
      success: false,
      duration,
      installationId,
      errorType: platyfendError.type,
    });

    return result;
  }
}

// Export singleton instance
export const installationWebhookHandler = new InstallationWebhookHandler();
