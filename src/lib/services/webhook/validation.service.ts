/**
 * Webhook Validation Service
 * Handles payload validation, rate limiting, and basic webhook processing logic
 */

import { z } from 'zod';
import { RateLimiter } from '../../utils/rate-limiter';

/**
 * Validation schemas for different webhook types
 */
export const PullRequestPayloadSchema = z.object({
  action: z.string(),
  pull_request: z.object({
    id: z.number(),
    number: z.number(),
    title: z.string(),
    state: z.string(),
    draft: z.boolean(),
    mergeable: z.boolean().nullable(),
    user: z.object({
      login: z.string(),
    }),
    head: z.object({
      ref: z.string(),
      sha: z.string(),
    }),
    base: z.object({
      ref: z.string(),
    }),
    html_url: z.string(),
    diff_url: z.string(),
    patch_url: z.string(),
    commits_url: z.string(),
    review_comments_url: z.string(),
    statuses_url: z.string(),
    created_at: z.string(),
    updated_at: z.string(),
    commits: z.number(),
    additions: z.number(),
    deletions: z.number(),
    changed_files: z.number(),
  }),
  repository: z.object({
    id: z.number(),
    full_name: z.string(),
    name: z.string(),
    owner: z.object({
      login: z.string(),
    }),
  }),
  sender: z.object({
    login: z.string(),
  }),
});

export const InstallationPayloadSchema = z.object({
  action: z.string(),
  installation: z.object({
    id: z.number(),
    account: z.object({
      login: z.string(),
      id: z.number(),
    }),
  }),
  repositories: z.array(z.object({
    id: z.number(),
    name: z.string(),
    full_name: z.string(),
  })).optional(),
  repository_selection: z.enum(['all', 'selected']).optional(),
});

export const RepositoryPayloadSchema = z.object({
  action: z.string(),
  repository: z.object({
    id: z.number(),
    name: z.string(),
    full_name: z.string(),
    private: z.boolean(),
    description: z.string().nullable(),
  }),
  sender: z.object({
    login: z.string(),
  }),
});

/**
 * Rate limiting configuration for different webhook types
 */
export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
}

export const WEBHOOK_RATE_LIMITS: Record<string, RateLimitConfig> = {
  pullRequest: { maxRequests: 20, windowMs: 300000 }, // 20 requests per 5 minutes
  installation: { maxRequests: 5, windowMs: 300000 }, // 5 requests per 5 minutes
  installationRepositories: { maxRequests: 10, windowMs: 300000 }, // 10 requests per 5 minutes
  repository: { maxRequests: 15, windowMs: 300000 }, // 15 requests per 5 minutes
};

/**
 * Validation result interface
 */
export interface ValidationResult<T = unknown> {
  isValid: boolean;
  data?: T;
  errors: string[];
  rateLimited?: boolean;
}

/**
 * Webhook Validation Service
 */
export class WebhookValidationService {
  private rateLimiter = new RateLimiter();

  /**
   * Validate pull request webhook payload
   */
  validatePullRequestPayload(payload: unknown, repositoryId?: string): ValidationResult<z.infer<typeof PullRequestPayloadSchema>> {
    const result: ValidationResult<z.infer<typeof PullRequestPayloadSchema>> = {
      isValid: false,
      errors: [],
    };

    // Rate limiting check
    if (repositoryId) {
      const rateLimitKey = `pr_${repositoryId}`;
      const config = WEBHOOK_RATE_LIMITS.pullRequest;
      
      if (!this.rateLimiter.isAllowed(rateLimitKey, config.maxRequests, config.windowMs)) {
        result.rateLimited = true;
        result.errors.push('Rate limit exceeded for pull request webhooks');
        return result;
      }
    }

    // Schema validation
    const validationResult = PullRequestPayloadSchema.safeParse(payload);
    if (!validationResult.success) {
      result.errors.push(`Invalid pull request payload: ${validationResult.error.message}`);
      return result;
    }

    result.isValid = true;
    result.data = validationResult.data;
    return result;
  }

  /**
   * Validate installation webhook payload
   */
  validateInstallationPayload(payload: unknown, installationId?: string): ValidationResult<z.infer<typeof InstallationPayloadSchema>> {
    const result: ValidationResult<z.infer<typeof InstallationPayloadSchema>> = {
      isValid: false,
      errors: [],
    };

    // Rate limiting check
    if (installationId) {
      const rateLimitKey = `installation_${installationId}`;
      const config = WEBHOOK_RATE_LIMITS.installation;
      
      if (!this.rateLimiter.isAllowed(rateLimitKey, config.maxRequests, config.windowMs)) {
        result.rateLimited = true;
        result.errors.push('Rate limit exceeded for installation webhooks');
        return result;
      }
    }

    // Schema validation
    const validationResult = InstallationPayloadSchema.safeParse(payload);
    if (!validationResult.success) {
      result.errors.push(`Invalid installation payload: ${validationResult.error.message}`);
      return result;
    }

    result.isValid = true;
    result.data = validationResult.data;
    return result;
  }

  /**
   * Validate installation repositories webhook payload
   */
  validateInstallationRepositoriesPayload(payload: unknown, installationId?: string): ValidationResult {
    const result: ValidationResult = {
      isValid: false,
      errors: [],
    };

    // Rate limiting check
    if (installationId) {
      const rateLimitKey = `installation_repos_${installationId}`;
      const config = WEBHOOK_RATE_LIMITS.installationRepositories;
      
      if (!this.rateLimiter.isAllowed(rateLimitKey, config.maxRequests, config.windowMs)) {
        result.rateLimited = true;
        result.errors.push('Rate limit exceeded for installation repositories webhooks');
        return result;
      }
    }

    // Basic validation (can be enhanced with schema if needed)
    if (!payload || typeof payload !== 'object') {
      result.errors.push('Invalid payload format');
      return result;
    }

    result.isValid = true;
    result.data = payload;
    return result;
  }

  /**
   * Validate repository webhook payload
   */
  validateRepositoryPayload(payload: unknown, repositoryId?: string): ValidationResult<z.infer<typeof RepositoryPayloadSchema>> {
    const result: ValidationResult<z.infer<typeof RepositoryPayloadSchema>> = {
      isValid: false,
      errors: [],
    };

    // Rate limiting check
    if (repositoryId) {
      const rateLimitKey = `repository_${repositoryId}`;
      const config = WEBHOOK_RATE_LIMITS.repository;
      
      if (!this.rateLimiter.isAllowed(rateLimitKey, config.maxRequests, config.windowMs)) {
        result.rateLimited = true;
        result.errors.push('Rate limit exceeded for repository webhooks');
        return result;
      }
    }

    // Schema validation
    const validationResult = RepositoryPayloadSchema.safeParse(payload);
    if (!validationResult.success) {
      result.errors.push(`Invalid repository payload: ${validationResult.error.message}`);
      return result;
    }

    result.isValid = true;
    result.data = validationResult.data;
    return result;
  }

  /**
   * Check if a specific rate limit key is allowed
   */
  isRateLimitAllowed(key: string, webhookType: keyof typeof WEBHOOK_RATE_LIMITS): boolean {
    const config = WEBHOOK_RATE_LIMITS[webhookType];
    return this.rateLimiter.isAllowed(key, config.maxRequests, config.windowMs);
  }

  /**
   * Get rate limit status for a key
   */
  getRateLimitStatus(key: string, webhookType: keyof typeof WEBHOOK_RATE_LIMITS) {
    const config = WEBHOOK_RATE_LIMITS[webhookType];
    return {
      allowed: this.rateLimiter.isAllowed(key, config.maxRequests, config.windowMs),
      limit: config.maxRequests,
      windowMs: config.windowMs,
    };
  }
}

// Export singleton instance
export const webhookValidationService = new WebhookValidationService();
