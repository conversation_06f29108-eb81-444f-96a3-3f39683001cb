/**
 * Webhook Metrics Service
 * Handles metrics collection, monitoring, and performance tracking for webhook processing
 */

/**
 * Webhook metrics data structure
 */
export interface WebhookMetrics {
  action: string;
  success: boolean;
  duration: number;
  timestamp: Date;
  repositoriesAffected?: number;
  organizationId?: string;
  installationId?: string;
  repositoryId?: string;
  errorType?: string;
  retryCount?: number;
}

/**
 * Aggregated metrics for reporting
 */
export interface WebhookMetricsAggregation {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageDuration: number;
  maxDuration: number;
  minDuration: number;
  errorsByType: Record<string, number>;
  requestsByAction: Record<string, number>;
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * Performance thresholds for monitoring
 * Adjusted for longer analysis operations that can take significant time
 */
export const PERFORMANCE_THRESHOLDS = {
  WARNING_DURATION_MS: 30000, // 30 seconds - analysis can take time
  CRITICAL_DURATION_MS: 60000, // 60 seconds - still acceptable for complex analysis
  MAX_ACCEPTABLE_DURATION_MS: 120000, // 120 seconds (2 minutes) - maximum for analysis operations
} as const;

/**
 * Webhook Metrics Service
 */
export class WebhookMetricsService {
  private metrics: WebhookMetrics[] = [];
  private readonly MAX_METRICS_HISTORY = 10000; // Keep last 10k metrics in memory

  /**
   * Record a webhook processing metric
   */
  recordMetric(metric: Omit<WebhookMetrics, 'timestamp'>): void {
    const fullMetric: WebhookMetrics = {
      ...metric,
      timestamp: new Date(),
    };

    this.metrics.push(fullMetric);

    // Keep only recent metrics in memory
    if (this.metrics.length > this.MAX_METRICS_HISTORY) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS_HISTORY);
    }

    // Log performance warnings
    this.checkPerformanceThresholds(fullMetric);

    // Log metric for monitoring (in production, send to monitoring service)
    this.logMetric(fullMetric);
  }

  /**
   * Record a simple metric with basic information
   */
  recordSimpleMetric(action: string, success: boolean, duration: number, additionalData?: Partial<WebhookMetrics>): void {
    this.recordMetric({
      action,
      success,
      duration,
      ...additionalData,
    });
  }

  /**
   * Get metrics for a specific time range
   */
  getMetrics(startTime?: Date, endTime?: Date): WebhookMetrics[] {
    let filteredMetrics = this.metrics;

    if (startTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= startTime);
    }

    if (endTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp <= endTime);
    }

    return filteredMetrics;
  }

  /**
   * Get aggregated metrics for a time range
   */
  getAggregatedMetrics(startTime?: Date, endTime?: Date): WebhookMetricsAggregation {
    const metrics = this.getMetrics(startTime, endTime);
    
    if (metrics.length === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageDuration: 0,
        maxDuration: 0,
        minDuration: 0,
        errorsByType: {},
        requestsByAction: {},
        timeRange: {
          start: startTime || new Date(),
          end: endTime || new Date(),
        },
      };
    }

    const durations = metrics.map(m => m.duration);
    const successfulRequests = metrics.filter(m => m.success).length;
    const failedRequests = metrics.length - successfulRequests;

    // Group errors by type
    const errorsByType: Record<string, number> = {};
    metrics.filter(m => !m.success && m.errorType).forEach(m => {
      const errorType = m.errorType!;
      errorsByType[errorType] = (errorsByType[errorType] || 0) + 1;
    });

    // Group requests by action
    const requestsByAction: Record<string, number> = {};
    metrics.forEach(m => {
      requestsByAction[m.action] = (requestsByAction[m.action] || 0) + 1;
    });

    return {
      totalRequests: metrics.length,
      successfulRequests,
      failedRequests,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      maxDuration: Math.max(...durations),
      minDuration: Math.min(...durations),
      errorsByType,
      requestsByAction,
      timeRange: {
        start: startTime || metrics[0].timestamp,
        end: endTime || metrics[metrics.length - 1].timestamp,
      },
    };
  }

  /**
   * Get recent performance statistics
   */
  getRecentPerformanceStats(minutesBack: number = 60): {
    averageDuration: number;
    successRate: number;
    requestCount: number;
    slowRequests: number;
    errorRate: number;
  } {
    const cutoffTime = new Date(Date.now() - minutesBack * 60 * 1000);
    const recentMetrics = this.getMetrics(cutoffTime);

    if (recentMetrics.length === 0) {
      return {
        averageDuration: 0,
        successRate: 0,
        requestCount: 0,
        slowRequests: 0,
        errorRate: 0,
      };
    }

    const successfulRequests = recentMetrics.filter(m => m.success).length;
    const slowRequests = recentMetrics.filter(m => m.duration > PERFORMANCE_THRESHOLDS.WARNING_DURATION_MS).length;
    const totalDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0);

    return {
      averageDuration: totalDuration / recentMetrics.length,
      successRate: (successfulRequests / recentMetrics.length) * 100,
      requestCount: recentMetrics.length,
      slowRequests,
      errorRate: ((recentMetrics.length - successfulRequests) / recentMetrics.length) * 100,
    };
  }

  /**
   * Check performance thresholds and log warnings
   */
  private checkPerformanceThresholds(metric: WebhookMetrics): void {
    if (metric.duration > PERFORMANCE_THRESHOLDS.CRITICAL_DURATION_MS) {
      console.error(`🚨 CRITICAL: Webhook processing took ${metric.duration}ms for action ${metric.action}`);
    } else if (metric.duration > PERFORMANCE_THRESHOLDS.WARNING_DURATION_MS) {
      console.warn(`⚠️ WARNING: Webhook processing took ${metric.duration}ms for action ${metric.action}`);
    }

    if (metric.duration > PERFORMANCE_THRESHOLDS.MAX_ACCEPTABLE_DURATION_MS) {
      console.error(`💥 TIMEOUT: Webhook processing exceeded maximum acceptable duration (${metric.duration}ms) for action ${metric.action}`);
    }
  }

  /**
   * Log metric for monitoring and debugging
   */
  private logMetric(metric: WebhookMetrics): void {
    const logLevel = metric.success ? 'info' : 'error';
    const logData = {
      action: metric.action,
      success: metric.success,
      duration: `${metric.duration}ms`,
      timestamp: metric.timestamp.toISOString(),
      ...(metric.repositoriesAffected && { repositoriesAffected: metric.repositoriesAffected }),
      ...(metric.organizationId && { organizationId: metric.organizationId }),
      ...(metric.installationId && { installationId: metric.installationId }),
      ...(metric.repositoryId && { repositoryId: metric.repositoryId }),
      ...(metric.errorType && { errorType: metric.errorType }),
      ...(metric.retryCount && { retryCount: metric.retryCount }),
    };

    if (logLevel === 'error') {
      console.error('📊 Webhook Metric (ERROR):', logData);
    } else {
      console.log('📊 Webhook Metric:', logData);
    }

    // In production, send to monitoring service (Datadog, New Relic, etc.)
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to monitoring service
      // Example: datadog.increment('webhook.processed', 1, [`action:${metric.action}`, `success:${metric.success}`]);
    }
  }

  /**
   * Clear old metrics (useful for memory management)
   */
  clearOldMetrics(olderThanHours: number = 24): number {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    const initialLength = this.metrics.length;
    
    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
    
    const removedCount = initialLength - this.metrics.length;
    if (removedCount > 0) {
      console.log(`🧹 Cleared ${removedCount} old webhook metrics (older than ${olderThanHours} hours)`);
    }
    
    return removedCount;
  }

  /**
   * Get health status based on recent metrics
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    details: Record<string, unknown>;
  } {
    const stats = this.getRecentPerformanceStats(15); // Last 15 minutes

    if (stats.requestCount === 0) {
      return {
        status: 'healthy',
        message: 'No recent webhook activity',
        details: stats,
      };
    }

    if (stats.errorRate > 50) {
      return {
        status: 'critical',
        message: `High error rate: ${stats.errorRate.toFixed(1)}%`,
        details: stats,
      };
    }

    if (stats.errorRate > 20 || stats.averageDuration > PERFORMANCE_THRESHOLDS.WARNING_DURATION_MS) {
      return {
        status: 'warning',
        message: `Performance degradation detected`,
        details: stats,
      };
    }

    return {
      status: 'healthy',
      message: 'Webhook processing is healthy',
      details: stats,
    };
  }
}

// Export singleton instance
export const webhookMetricsService = new WebhookMetricsService();
