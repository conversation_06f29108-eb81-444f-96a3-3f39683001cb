/**
 * Repository Webhook Handler
 * Handles repository webhook events (created, deleted, renamed, etc.)
 */

import { z } from 'zod';
import { WebhookSyncResult } from '@/src/lib/types/webhooks';
import { webhookValidationService, RepositoryPayloadSchema } from './validation.service';
import { webhookMetricsService } from './metrics.service';
import { RepositoryHandlers } from '../repository-handlers';
import { Organization } from '@/src/lib/database/models';
import { errorHandlingService, createErrorContext } from '../error-handling';

/**
 * Repository processing configuration
 */
interface RepositoryProcessingConfig {
  enableMetadataSync: boolean;
  enableStatusUpdates: boolean;
}

const DEFAULT_REPOSITORY_CONFIG: RepositoryProcessingConfig = {
  enableMetadataSync: true,
  enableStatusUpdates: true,
};

/**
 * Repository Webhook Handler
 */
export class RepositoryWebhookHandler {
  private repositoryHandlers = new RepositoryHandlers();
  private config: RepositoryProcessingConfig;

  constructor(config: Partial<RepositoryProcessingConfig> = {}) {
    this.config = { ...DEFAULT_REPOSITORY_CONFIG, ...config };
  }

  /**
   * Handle repository webhook event
   */
  async handleRepositoryEvent(payload: unknown): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const result: WebhookSyncResult = {
      success: false,
      action: 'repository.unknown',
      repositoriesAffected: 0,
      errors: [],
    };

    try {
      // Extract repository ID for validation
      const repositoryId = this.extractRepositoryId(payload);
      
      // Validate payload
      const validation = webhookValidationService.validateRepositoryPayload(payload, repositoryId);

      if (!validation.isValid) {
        result.errors = validation.errors;
        if (validation.rateLimited) {
          result.action = 'repository.rate_limited';
        }
        this.recordMetrics(result, startTime, repositoryId);
        return result;
      }

      const validatedPayload = validation.data!;
      result.action = `repository.${validatedPayload.action}`;

      // Find organizations that have this repository
      const organizations = await this.findOrganizationsWithRepository(repositoryId!);
      
      if (organizations.length === 0) {
        console.log(`No organizations found with repository ${repositoryId}, skipping processing`);
        result.success = true;
        this.recordMetrics(result, startTime, repositoryId);
        return result;
      }

      // Process based on action type
      switch (validatedPayload.action) {
        case 'created':
          await this.handleRepositoryCreated(validatedPayload, organizations, result);
          break;
        case 'deleted':
          await this.handleRepositoryDeleted(validatedPayload, organizations, result);
          break;
        case 'renamed':
          await this.handleRepositoryRenamed(validatedPayload, organizations, result);
          break;
        case 'edited':
          await this.handleRepositoryEdited(validatedPayload, organizations, result);
          break;
        case 'archived':
          await this.handleRepositoryArchived(validatedPayload, organizations, result);
          break;
        case 'unarchived':
          await this.handleRepositoryUnarchived(validatedPayload, organizations, result);
          break;
        case 'publicized':
          await this.handleRepositoryPublicized(validatedPayload, organizations, result);
          break;
        case 'privatized':
          await this.handleRepositoryPrivatized(validatedPayload, organizations, result);
          break;
        case 'transferred':
          await this.handleRepositoryTransferred(validatedPayload, organizations, result);
          break;
        default:
          result.errors.push(`Unsupported repository action: ${validatedPayload.action}`);
      }

      result.success = result.errors.length === 0;
      this.recordMetrics(result, startTime, repositoryId);
      return result;

    } catch (error) {
      return this.handleError(error, payload, startTime, result);
    }
  }

  /**
   * Handle repository created event
   */
  private async handleRepositoryCreated(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      console.log(`📦 Processing repository created: ${payload.repository.full_name}`);

      for (const organization of organizations) {
        try {
          if (this.config.enableMetadataSync) {
            await this.repositoryHandlers.handleRepositoryCreated(
              organization,
              payload.repository,
              result
            );
          }
          result.repositoriesAffected++;
        } catch (error) {
          result.errors.push(
            `Failed to update repository ${payload.repository.id} in organization ${organization._id}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }
    } catch (error) {
      result.errors.push(`Failed to process repository created: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repository deleted event
   */
  private async handleRepositoryDeleted(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      console.log(`🗑️ Processing repository deleted: ${payload.repository.full_name}`);

      for (const organization of organizations) {
        try {
          await this.repositoryHandlers.handleRepositoryDeleted(
            organization,
            payload.repository.id.toString(),
            result
          );
          result.repositoriesAffected++;
        } catch (error) {
          result.errors.push(
            `Failed to remove repository ${payload.repository.id} from organization ${organization._id}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }
    } catch (error) {
      result.errors.push(`Failed to process repository deleted: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repository renamed event
   */
  private async handleRepositoryRenamed(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      console.log(`✏️ Processing repository renamed: ${payload.repository.full_name}`);

      for (const organization of organizations) {
        try {
          if (this.config.enableMetadataSync) {
            await this.repositoryHandlers.handleRepositoryUpdated(
              organization,
              payload.repository,
              result
            );
          }
          result.repositoriesAffected++;
        } catch (error) {
          result.errors.push(
            `Failed to update repository ${payload.repository.id} in organization ${organization._id}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }
    } catch (error) {
      result.errors.push(`Failed to process repository renamed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repository edited event
   */
  private async handleRepositoryEdited(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      console.log(`📝 Processing repository edited: ${payload.repository.full_name}`);

      for (const organization of organizations) {
        try {
          if (this.config.enableMetadataSync) {
            await this.repositoryHandlers.handleRepositoryUpdated(
              organization,
              payload.repository,
              result
            );
          }
          result.repositoriesAffected++;
        } catch (error) {
          result.errors.push(
            `Failed to update repository ${payload.repository.id} in organization ${organization._id}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }
    } catch (error) {
      result.errors.push(`Failed to process repository edited: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repository archived event
   */
  private async handleRepositoryArchived(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    await this.handleRepositoryStatusChange(payload, organizations, result, 'archived', true);
  }

  /**
   * Handle repository unarchived event
   */
  private async handleRepositoryUnarchived(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    await this.handleRepositoryStatusChange(payload, organizations, result, 'unarchived', false);
  }

  /**
   * Handle repository publicized event
   */
  private async handleRepositoryPublicized(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    await this.handleRepositoryVisibilityChange(payload, organizations, result, 'publicized', false);
  }

  /**
   * Handle repository privatized event
   */
  private async handleRepositoryPrivatized(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    await this.handleRepositoryVisibilityChange(payload, organizations, result, 'privatized', true);
  }

  /**
   * Handle repository transferred event
   */
  private async handleRepositoryTransferred(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult
  ): Promise<void> {
    try {
      console.log(`🔄 Processing repository transferred: ${payload.repository.full_name}`);
      
      // For transfers, we typically need to remove from old organization
      // and add to new one, but this requires more complex logic
      result.errors.push('Repository transfer handling not yet implemented');
    } catch (error) {
      result.errors.push(`Failed to process repository transferred: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repository status changes (archived/unarchived)
   */
  private async handleRepositoryStatusChange(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult,
    action: string,
    archived: boolean
  ): Promise<void> {
    try {
      console.log(`📋 Processing repository ${action}: ${payload.repository.full_name}`);

      for (const organization of organizations) {
        try {
          if (this.config.enableMetadataSync) {
            await this.repositoryHandlers.handleRepositoryUpdated(
              organization,
              payload.repository,
              result
            );
          }
          result.repositoriesAffected++;
        } catch (error) {
          result.errors.push(
            `Failed to update repository ${payload.repository.id} in organization ${organization._id}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }
    } catch (error) {
      result.errors.push(`Failed to process repository ${action}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle repository visibility changes (public/private)
   */
  private async handleRepositoryVisibilityChange(
    payload: z.infer<typeof RepositoryPayloadSchema>,
    organizations: any[],
    result: WebhookSyncResult,
    action: string,
    isPrivate: boolean
  ): Promise<void> {
    try {
      console.log(`👁️ Processing repository ${action}: ${payload.repository.full_name}`);

      for (const organization of organizations) {
        try {
          if (this.config.enableMetadataSync) {
            await this.repositoryHandlers.handleRepositoryUpdated(
              organization,
              payload.repository,
              result
            );
          }
          result.repositoriesAffected++;
        } catch (error) {
          result.errors.push(
            `Failed to update repository ${payload.repository.id} in organization ${organization._id}: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          );
        }
      }
    } catch (error) {
      result.errors.push(`Failed to process repository ${action}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Find organizations that have this repository
   */
  private async findOrganizationsWithRepository(repositoryId: string): Promise<any[]> {
    return Organization.find({
      'repos.repo_id': repositoryId
    });
  }

  /**
   * Extract repository ID from payload
   */
  private extractRepositoryId(payload: unknown): string | undefined {
    if (payload && typeof payload === 'object' && 'repository' in payload) {
      const repository = (payload as any).repository;
      return repository?.id?.toString();
    }
    return undefined;
  }

  /**
   * Record metrics for the webhook processing
   */
  private recordMetrics(result: WebhookSyncResult, startTime: number, repositoryId?: string): void {
    const duration = Date.now() - startTime;
    webhookMetricsService.recordMetric({
      action: result.action,
      success: result.success,
      duration,
      repositoriesAffected: result.repositoriesAffected,
      repositoryId,
      errorType: result.errors.length > 0 ? 'processing_error' : undefined,
    });
  }

  /**
   * Handle errors during processing
   */
  private handleError(
    error: unknown,
    payload: unknown,
    startTime: number,
    result: WebhookSyncResult
  ): WebhookSyncResult {
    const duration = Date.now() - startTime;
    const repositoryId = this.extractRepositoryId(payload);

    const context = createErrorContext(
      undefined, // userId
      undefined, // organizationId
      undefined, // installationId
      repositoryId,
      'repository_webhook'
    );

    const platyfendError = errorHandlingService.handleWebhookError(error, context);
    errorHandlingService.logError(platyfendError);

    result.success = false;
    result.errors.push(platyfendError.message);
    result.metrics = {
      duration,
      timestamp: new Date(),
    };

    webhookMetricsService.recordMetric({
      action: result.action,
      success: false,
      duration,
      repositoryId,
      errorType: platyfendError.type,
    });

    return result;
  }
}

// Export singleton instance
export const repositoryWebhookHandler = new RepositoryWebhookHandler();
