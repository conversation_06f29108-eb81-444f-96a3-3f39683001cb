/**
 * Webhook Orchestrator
 * Main service that coordinates webhook processing using specialized handlers
 */

import { WebhookSyncResult } from '@/src/lib/types/webhooks';
import { pullRequestWebhookHandler } from './pull-request.handler';
import { installationWebhookHandler } from './installation.handler';
import { repositoryWebhookHandler } from './repository.handler';
import { webhookMetricsService } from './metrics.service';

/**
 * Webhook processing configuration
 */
export interface WebhookOrchestratorConfig {
  enableMetrics: boolean;
  enableHealthChecks: boolean;
  logProcessingDetails: boolean;
}

const DEFAULT_CONFIG: WebhookOrchestratorConfig = {
  enableMetrics: true,
  enableHealthChecks: true,
  logProcessingDetails: true,
};

/**
 * Webhook headers interface
 */
export interface WebhookHeaders {
  'x-github-event': string;
  'x-github-delivery': string;
  'x-github-hook-id'?: string;
  'x-github-hook-installation-target-id'?: string;
  'x-github-hook-installation-target-type'?: string;
  'x-hub-signature-256'?: string;
  'user-agent'?: string;
}

/**
 * Webhook Orchestrator
 * Coordinates webhook processing across specialized handlers
 */
export class WebhookOrchestrator {
  private config: WebhookOrchestratorConfig;

  constructor(config: Partial<WebhookOrchestratorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Process webhook based on event type
   */
  async processWebhook(
    eventType: string,
    payload: unknown,
    headers?: Partial<WebhookHeaders>
  ): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    
    if (this.config.logProcessingDetails) {
      console.log(`🔄 Processing webhook: ${eventType}`, {
        delivery: headers?.['x-github-delivery'],
        timestamp: new Date().toISOString(),
      });
    }

    try {
      let result: WebhookSyncResult;

      // Route to appropriate handler based on event type
      switch (eventType) {
        case 'pull_request':
          result = await this.processPullRequestWebhook(payload, headers);
          break;
        case 'installation':
          result = await this.processInstallationWebhook(payload);
          break;
        case 'installation_repositories':
          result = await this.processInstallationRepositoriesWebhook(payload);
          break;
        case 'repository':
          result = await this.processRepositoryWebhook(payload);
          break;
        case 'push':
          result = await this.processPushWebhook(payload);
          break;
        default:
          result = this.createUnsupportedEventResult(eventType);
      }

      // Log processing result
      if (this.config.logProcessingDetails) {
        this.logProcessingResult(eventType, result, startTime);
      }

      return result;

    } catch (error) {
      return this.handleGlobalError(error, eventType, startTime);
    }
  }

  /**
   * Process pull request webhook
   */
  private async processPullRequestWebhook(
    payload: unknown,
    headers?: Partial<WebhookHeaders>
  ): Promise<WebhookSyncResult> {
    const webhookHeaders = headers ? {
      event: headers['x-github-event'] || 'pull_request',
      delivery: headers['x-github-delivery'] || 'unknown',
    } : undefined;

    return pullRequestWebhookHandler.handlePullRequestEvent(payload, webhookHeaders);
  }

  /**
   * Process installation webhook
   */
  private async processInstallationWebhook(payload: unknown): Promise<WebhookSyncResult> {
    return installationWebhookHandler.handleInstallationEvent(payload);
  }

  /**
   * Process installation repositories webhook
   */
  private async processInstallationRepositoriesWebhook(payload: unknown): Promise<WebhookSyncResult> {
    return installationWebhookHandler.handleInstallationRepositoriesEvent(payload);
  }

  /**
   * Process repository webhook
   */
  private async processRepositoryWebhook(payload: unknown): Promise<WebhookSyncResult> {
    return repositoryWebhookHandler.handleRepositoryEvent(payload);
  }

  /**
   * Process push webhook (basic implementation)
   */
  private async processPushWebhook(payload: unknown): Promise<WebhookSyncResult> {
    // Basic push webhook handling - can be expanded later
    return {
      success: true,
      action: 'push.processed',
      repositoriesAffected: 1,
      errors: [],
      metrics: {
        duration: 0,
        timestamp: new Date(),
      },
    };
  }

  /**
   * Create result for unsupported event types
   */
  private createUnsupportedEventResult(eventType: string): WebhookSyncResult {
    return {
      success: false,
      action: `${eventType}.unsupported`,
      repositoriesAffected: 0,
      errors: [`Unsupported webhook event type: ${eventType}`],
      metrics: {
        duration: 0,
        timestamp: new Date(),
      },
    };
  }

  /**
   * Handle global processing errors
   */
  private handleGlobalError(
    error: unknown,
    eventType: string,
    startTime: number
  ): WebhookSyncResult {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    console.error(`❌ Global webhook processing error for ${eventType}:`, error);

    if (this.config.enableMetrics) {
      webhookMetricsService.recordMetric({
        action: `${eventType}.global_error`,
        success: false,
        duration,
        errorType: 'global_processing_error',
      });
    }

    return {
      success: false,
      action: `${eventType}.error`,
      repositoriesAffected: 0,
      errors: [`Global processing error: ${errorMessage}`],
      metrics: {
        duration,
        timestamp: new Date(),
      },
    };
  }

  /**
   * Log processing result
   */
  private logProcessingResult(
    eventType: string,
    result: WebhookSyncResult,
    startTime: number
  ): void {
    const duration = Date.now() - startTime;
    const logData = {
      eventType,
      action: result.action,
      success: result.success,
      duration: `${duration}ms`,
      repositoriesAffected: result.repositoriesAffected,
      errors: result.errors.length > 0 ? result.errors : undefined,
      organizationId: result.organizationId,
    };

    if (result.success) {
      console.log('✅ Webhook processed successfully:', logData);
    } else {
      console.error('❌ Webhook processing failed:', logData);
    }
  }

  /**
   * Get processing health status
   */
  getHealthStatus() {
    if (!this.config.enableHealthChecks) {
      return {
        status: 'unknown',
        message: 'Health checks disabled',
      };
    }

    return webhookMetricsService.getHealthStatus();
  }

  /**
   * Get processing metrics
   */
  getMetrics(startTime?: Date, endTime?: Date) {
    if (!this.config.enableMetrics) {
      return null;
    }

    return webhookMetricsService.getAggregatedMetrics(startTime, endTime);
  }

  /**
   * Get recent performance statistics
   */
  getPerformanceStats(minutesBack: number = 60) {
    if (!this.config.enableMetrics) {
      return null;
    }

    return webhookMetricsService.getRecentPerformanceStats(minutesBack);
  }

  /**
   * Clear old metrics for memory management
   */
  clearOldMetrics(olderThanHours: number = 24): number {
    if (!this.config.enableMetrics) {
      return 0;
    }

    return webhookMetricsService.clearOldMetrics(olderThanHours);
  }
}

// Export singleton instance
export const webhookOrchestrator = new WebhookOrchestrator();

// Export convenience functions for backward compatibility
export async function handleInstallationWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return webhookOrchestrator.processWebhook('installation', payload);
}

export async function handleInstallationRepositoriesWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return webhookOrchestrator.processWebhook('installation_repositories', payload);
}

export async function handlePullRequestWebhook(
  payload: unknown,
  headers?: Partial<WebhookHeaders>
): Promise<WebhookSyncResult> {
  return webhookOrchestrator.processWebhook('pull_request', payload, headers);
}

export async function handleRepositoryWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return webhookOrchestrator.processWebhook('repository', payload);
}

export async function handlePushWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return webhookOrchestrator.processWebhook('push', payload);
}
