import { z } from 'zod';

// TypeScript interfaces for webhook payloads
export interface GitHubPullRequestPayload {
  action: 'opened' | 'reopened' | 'closed' | 'edited' | 'synchronize';
  pull_request: {
    id: number;
    number: number;
    title: string;
    state: 'open' | 'closed';
    user: { login: string };
    html_url: string;
    base: { ref: string };
    head: { ref: string; sha: string };
    draft: boolean;
    mergeable: boolean | null;
    created_at: string;
    updated_at: string;
    diff_url: string;
    patch_url: string;
    commits: number;
    additions: number;
    deletions: number;
    changed_files: number;
    commits_url: string;
    review_comments_url: string;
    statuses_url: string;
  };
  repository: {
    id: number;
    name: string;
    full_name: string;
    owner: {
      login: string;
      id: number;
    };
  };
  sender: {
    login: string;
  };
}

export interface GitHubInstallationPayload {
  action: 'created' | 'deleted' | 'suspend' | 'unsuspend';
  installation: {
    id: number;
    account: {
      id: number;
      login: string;
      type: 'User' | 'Organization';
    };
  };
  repositories?: Array<{
    id: number;
    name: string;
    full_name: string;
    private: boolean;
    permissions?: Record<string, boolean>;
  }>;
  repository_selection?: 'all' | 'selected';
}

export interface WebhookSyncResult {
  success: boolean;
  action: string;
  organizationId?: string;
  repositoriesAffected: number;
  errors: string[];
  metrics?: {
    duration: number;
    timestamp: Date;
  };
}

// Validation schemas
export const PullRequestPayloadSchema = z.object({
  action: z.enum(['opened', 'reopened', 'closed', 'edited', 'synchronize']),
  pull_request: z.object({
    id: z.number(),
    number: z.number(),
    title: z.string(),
    state: z.enum(['open', 'closed']),
    user: z.object({ login: z.string() }),
    html_url: z.string().url(),
    base: z.object({ ref: z.string() }),
    head: z.object({ ref: z.string(), sha: z.string() }),
    draft: z.boolean(),
    mergeable: z.boolean().nullable(),
    created_at: z.string(),
    updated_at: z.string(),
    diff_url: z.string().url(),
    patch_url: z.string().url(),
    commits: z.number(),
    additions: z.number(),
    deletions: z.number(),
    changed_files: z.number(),
    commits_url: z.string().url(),
    review_comments_url: z.string().url(),
    statuses_url: z.string().url(),
  }),
  repository: z.object({
    id: z.number(),
    name: z.string(),
    full_name: z.string(),
    owner: z.object({
      login: z.string(),
      id: z.number(),
    }),
  }),
  sender: z.object({
    login: z.string(),
  }),
});
