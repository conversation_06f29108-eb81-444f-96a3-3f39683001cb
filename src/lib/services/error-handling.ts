import { Organization, InstallationStatus } from '@/src/lib/database/models';
import { githubAppAuth } from '@/src/lib/github/app-auth';

export enum ErrorType {
  // API Errors
  GITHUB_API_ERROR = 'github_api_error',
  EXTERNAL_API_ERROR = 'external_api_error',
  INTERNAL_API_ERROR = 'internal_api_error',

  // Authentication & Authorization
  AUTH_ERROR = 'auth_error',
  PERMISSION_ERROR = 'permission_error',
  TOKEN_ERROR = 'token_error',

  // Application Errors
  INSTALLATION_ERROR = 'installation_error',
  WEBHOOK_ERROR = 'webhook_error',
  SYNC_ERROR = 'sync_error',

  // Infrastructure Errors
  DATABASE_ERROR = 'database_error',
  NETWORK_ERROR = 'network_error',
  TIMEOUT_ERROR = 'timeout_error',

  // Validation Errors
  VALIDATION_ERROR = 'validation_error',
  SCHEMA_ERROR = 'schema_error',

  // Business Logic Errors
  BUSINESS_RULE_ERROR = 'business_rule_error',
  RATE_LIMIT_ERROR = 'rate_limit_error',

  // System Errors
  CONFIGURATION_ERROR = 'configuration_error',
  DEPENDENCY_ERROR = 'dependency_error',
  UNKNOWN_ERROR = 'unknown_error',
}

export interface ErrorContext {
  // User context
  userId?: string;
  sessionId?: string;
  userAgent?: string;

  // Organization context
  organizationId?: string;
  installationId?: string;
  repositoryId?: string;

  // Operation context
  operation?: string;
  endpoint?: string;
  method?: string;
  requestId?: string;

  // Environment context
  environment?: 'development' | 'staging' | 'production';
  version?: string;
  buildId?: string;

  // Timing context
  timestamp: Date;
  duration?: number;

  // Additional metadata
  metadata?: Record<string, unknown>;
  tags?: string[];
}

export interface RecoveryAction {
  type: 'retry' | 'manual_sync' | 'reinstall_app' | 'contact_support' | 'check_permissions' |
        'refresh_token' | 'clear_cache' | 'restart_service' | 'update_config' | 'wait_and_retry';
  description: string;
  url?: string;
  automated?: boolean;
  priority: 'low' | 'medium' | 'high';
  estimatedTime?: string;
  parameters?: Record<string, unknown>;
}

export interface ErrorDetails {
  // Original error information
  originalError?: Error;
  stack?: string;
  cause?: string;

  // HTTP/API specific
  statusCode?: number;
  statusText?: string;
  headers?: Record<string, string>;

  // Validation specific
  validationErrors?: Array<{
    field: string;
    message: string;
    value?: unknown;
  }>;

  // Additional context
  correlationId?: string;
  traceId?: string;
  spanId?: string;
}

export interface PlatyfendError {
  // Core error information
  id: string; // Unique error ID
  type: ErrorType;
  message: string;
  userMessage: string;

  // Context and details
  context: ErrorContext;
  details: ErrorDetails;

  // Recovery and handling
  recoveryActions: RecoveryAction[];
  retryable: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';

  // Categorization
  category: 'user' | 'system' | 'external' | 'configuration';
  component: string;

  // Monitoring and alerting
  alerting: {
    shouldAlert: boolean;
    alertLevel: 'info' | 'warning' | 'error' | 'critical';
    notificationChannels: string[];
  };

  // Metrics and tracking
  metrics: {
    errorCount: number;
    firstOccurrence: Date;
    lastOccurrence: Date;
    affectedUsers: number;
  };
}

/**
 * Enhanced Error handling and recovery service
 */
export class ErrorHandlingService {
  private errorCounts = new Map<string, number>();
  private errorHistory = new Map<string, Date[]>();
  private readonly maxHistorySize = 100;

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Track error occurrence for metrics
   */
  private trackError(errorType: ErrorType, context: ErrorContext): void {
    const key = `${errorType}_${context.operation || 'unknown'}`;

    // Update count
    this.errorCounts.set(key, (this.errorCounts.get(key) || 0) + 1);

    // Update history
    const history = this.errorHistory.get(key) || [];
    history.push(new Date());

    // Keep only recent history
    if (history.length > this.maxHistorySize) {
      history.splice(0, history.length - this.maxHistorySize);
    }

    this.errorHistory.set(key, history);
  }

  /**
   * Get error metrics
   */
  getErrorMetrics(errorType?: ErrorType): Record<string, { count: number; recentOccurrences: Date[] }> {
    const metrics: Record<string, { count: number; recentOccurrences: Date[] }> = {};

    for (const [key, count] of this.errorCounts.entries()) {
      if (!errorType || key.startsWith(errorType)) {
        metrics[key] = {
          count,
          recentOccurrences: this.errorHistory.get(key) || []
        };
      }
    }

    return metrics;
  }

  /**
   * Create base error structure
   */
  protected createBaseError(
    type: ErrorType,
    message: string,
    userMessage: string,
    context: ErrorContext,
    originalError?: Error
  ): PlatyfendError {
    this.trackError(type, context);

    const errorId = this.generateErrorId();
    const key = `${type}_${context.operation || 'unknown'}`;
    const history = this.errorHistory.get(key) || [];

    return {
      id: errorId,
      type,
      message,
      userMessage,
      context: {
        ...context,
        environment: process.env.NODE_ENV as any || 'development',
        version: process.env.APP_VERSION,
        buildId: process.env.BUILD_ID,
      },
      details: {
        originalError,
        stack: originalError?.stack,
        cause: originalError?.cause as string,
        correlationId: context.requestId,
      },
      recoveryActions: [],
      retryable: false,
      severity: 'medium',
      category: 'system',
      component: context.operation || 'unknown',
      alerting: {
        shouldAlert: false,
        alertLevel: 'error',
        notificationChannels: [],
      },
      metrics: {
        errorCount: this.errorCounts.get(key) || 1,
        firstOccurrence: history[0] || new Date(),
        lastOccurrence: new Date(),
        affectedUsers: 1, // This would be calculated based on actual user tracking
      },
    };
  }

  /**
   * Handle GitHub API errors with appropriate recovery actions
   */
  handleGitHubAPIError(error: any, context: ErrorContext): PlatyfendError {
    const timestamp = new Date();
    const baseError = this.createBaseError(
      ErrorType.GITHUB_API_ERROR,
      `GitHub API error: ${error.message || 'Unknown error'}`,
      'GitHub API request failed',
      context,
      error
    );

    // Parse GitHub API error
    if (error.response?.status === 401) {
      return {
        ...baseError,
        userMessage: 'GitHub app authentication has expired. Please reinstall the app.',
        details: {
          ...baseError.details,
          statusCode: 401,
          statusText: 'Unauthorized',
        },
        recoveryActions: [
          {
            type: 'reinstall_app',
            description: 'Reinstall the GitHub app to restore access',
            automated: false,
            priority: 'high',
            estimatedTime: '2-3 minutes'
          },
          {
            type: 'refresh_token',
            description: 'Attempt to refresh authentication token',
            automated: true,
            priority: 'medium',
            estimatedTime: '30 seconds'
          }
        ],
        retryable: false,
        severity: 'high',
        category: 'external',
        alerting: {
          shouldAlert: true,
          alertLevel: 'error',
          notificationChannels: ['slack', 'email']
        }
      };
    }

    if (error.response?.status === 403) {
      return {
        ...baseError,
        type: ErrorType.PERMISSION_ERROR,
        userMessage: 'The GitHub app doesn\'t have permission to access this resource.',
        details: {
          ...baseError.details,
          statusCode: 403,
          statusText: 'Forbidden',
        },
        recoveryActions: [
          {
            type: 'check_permissions',
            description: 'Check GitHub app permissions and repository access',
            url: `https://github.com/settings/installations`,
            automated: false,
            priority: 'high',
            estimatedTime: '5-10 minutes'
          },
          {
            type: 'reinstall_app',
            description: 'Reinstall app with correct permissions',
            automated: false,
            priority: 'medium',
            estimatedTime: '2-3 minutes'
          }
        ],
        retryable: false,
        severity: 'medium',
        category: 'external',
        alerting: {
          shouldAlert: true,
          alertLevel: 'warning',
          notificationChannels: ['slack']
        }
      };
    }

    if (error.response?.status === 404) {
      const notFoundError = this.createBaseError(
        ErrorType.GITHUB_API_ERROR,
        'GitHub resource not found',
        'The requested repository or installation was not found.',
        { ...context, timestamp },
        error
      );

      return {
        ...notFoundError,
        recoveryActions: [
          {
            type: 'manual_sync',
            description: 'Sync repositories to update the list',
            automated: true,
            priority: 'medium'
          }
        ],
        retryable: false,
        severity: 'low'
      };
    }

    if (error.response?.status >= 500) {
      const serverError = this.createBaseError(
        ErrorType.GITHUB_API_ERROR,
        'GitHub API server error',
        'GitHub is experiencing issues. Please try again later.',
        { ...context, timestamp },
        error
      );

      return {
        ...serverError,
        recoveryActions: [
          {
            type: 'retry',
            description: 'Retry the operation after a short delay',
            automated: true,
            priority: 'medium'
          }
        ],
        retryable: true,
        severity: 'medium'
      };
    }

    // Rate limiting
    if (error.response?.status === 429) {
      const resetTime = error.response.headers['x-ratelimit-reset'];
      const rateLimitError = this.createBaseError(
        ErrorType.GITHUB_API_ERROR,
        'GitHub API rate limit exceeded',
        'Too many requests to GitHub. Please wait a moment and try again.',
        { ...context, timestamp },
        error
      );

      return {
        ...rateLimitError,
        recoveryActions: [
          {
            type: 'retry',
            description: `Retry after rate limit resets${resetTime ? ` at ${new Date(resetTime * 1000).toLocaleTimeString()}` : ''}`,
            automated: true,
            priority: 'low'
          }
        ],
        retryable: true,
        severity: 'low'
      };
    }

    // Generic GitHub API error
    const genericError = this.createBaseError(
      ErrorType.GITHUB_API_ERROR,
      `GitHub API error: ${error.message}`,
      'Unable to connect to GitHub. Please check your connection and try again.',
      { ...context, timestamp },
      error
    );

    return {
      ...genericError,
      recoveryActions: [
        {
          type: 'retry',
          description: 'Retry the operation',
          automated: true,
          priority: 'medium'
        }
      ],
      retryable: true,
      severity: 'medium'
    };
  }

  /**
   * Handle installation errors
   */
  handleInstallationError(error: any, context: ErrorContext): PlatyfendError {
    const timestamp = new Date();

    if (error.message?.includes('installation not found')) {
      const baseError = this.createBaseError(
        ErrorType.INSTALLATION_ERROR,
        'GitHub app installation not found',
        'The GitHub app is not installed or has been removed.',
        { ...context, timestamp },
        error
      );

      return {
        ...baseError,
        recoveryActions: [
          {
            type: 'reinstall_app',
            description: 'Install the GitHub app to access repositories',
            automated: false,
            priority: 'high'
          }
        ],
        retryable: false,
        severity: 'high'
      };
    }

    if (error.message?.includes('suspended')) {
      const baseError = this.createBaseError(
        ErrorType.INSTALLATION_ERROR,
        'GitHub app installation suspended',
        'The GitHub app installation has been suspended.',
        { ...context, timestamp },
        error
      );

      return {
        ...baseError,
        recoveryActions: [
          {
            type: 'check_permissions',
            description: 'Check GitHub app status and reactivate if needed',
            url: 'https://github.com/settings/installations',
            automated: false,
            priority: 'high'
          }
        ],
        retryable: false,
        severity: 'high'
      };
    }

    const baseError = this.createBaseError(
      ErrorType.INSTALLATION_ERROR,
      `Installation error: ${error.message}`,
      'There was a problem with the GitHub app installation.',
      { ...context, timestamp },
      error
    );

    return {
      ...baseError,
      recoveryActions: [
        {
          type: 'reinstall_app',
          description: 'Reinstall the GitHub app',
          automated: false,
          priority: 'medium'
        }
      ],
      retryable: false,
      severity: 'medium'
    };
  }

  /**
   * Handle webhook processing errors
   */
  handleWebhookError(error: any, context: ErrorContext): PlatyfendError {
    const timestamp = new Date();
    const baseError = this.createBaseError(
      ErrorType.WEBHOOK_ERROR,
      `Webhook processing error: ${error.message}`,
      'Failed to process GitHub webhook. Repository data may be temporarily out of sync.',
      { ...context, timestamp },
      error
    );

    return {
      ...baseError,
      recoveryActions: [
        {
          type: 'manual_sync',
          description: 'Manually sync repositories to ensure data consistency',
          automated: true,
          priority: 'medium'
        }
      ],
      retryable: true,
      severity: 'low'
    };
  }

  /**
   * Handle sync errors
   */
  handleSyncError(error: any, context: ErrorContext): PlatyfendError {
    const timestamp = new Date();
    const baseError = this.createBaseError(
      ErrorType.SYNC_ERROR,
      `Sync error: ${error.message}`,
      'Failed to synchronize repository data. Please try again.',
      { ...context, timestamp },
      error
    );

    return {
      ...baseError,
      recoveryActions: [
        {
          type: 'manual_sync',
          description: 'Manually sync repositories',
          automated: true,
          priority: 'medium'
        }
      ],
      retryable: true,
      severity: 'medium'
    };
  }

  /**
   * Handle database errors
   */
  handleDatabaseError(error: any, context: ErrorContext): PlatyfendError {
    const timestamp = new Date();

    if (error.name === 'ValidationError') {
      const baseError = this.createBaseError(
        ErrorType.DATABASE_ERROR,
        `Database validation error: ${error.message}`,
        'Invalid data detected. Please try again.',
        { ...context, timestamp },
        error
      );

      return {
        ...baseError,
        recoveryActions: [
          {
            type: 'retry',
            description: 'Retry with corrected data',
            automated: false,
            priority: 'medium'
          }
        ],
        retryable: false,
        severity: 'medium'
      };
    }

    if (error.name === 'MongoNetworkError') {
      const baseError = this.createBaseError(
        ErrorType.DATABASE_ERROR,
        'Database connection error',
        'Unable to connect to the database. Please try again later.',
        { ...context, timestamp },
        error
      );

      return {
        ...baseError,
        recoveryActions: [
          {
            type: 'retry',
            description: 'Retry the operation',
            automated: true,
            priority: 'high'
          }
        ],
        retryable: true,
        severity: 'high'
      };
    }

    const baseError = this.createBaseError(
      ErrorType.DATABASE_ERROR,
      `Database error: ${error.message}`,
      'A database error occurred. Please try again.',
      { ...context, timestamp },
      error
    );

    return {
      ...baseError,
      recoveryActions: [
        {
          type: 'retry',
          description: 'Retry the operation',
          automated: true,
          priority: 'medium'
        }
      ],
      retryable: true,
      severity: 'medium'
    };
  }

  /**
   * Attempt automatic recovery for retryable errors
   */
  async attemptRecovery(error: PlatyfendError, maxRetries: number = 3): Promise<boolean> {
    if (!error.retryable) {
      return false;
    }

    const retryAction = error.recoveryActions.find(action => action.type === 'retry' && action.automated);
    if (!retryAction) {
      return false;
    }

    // Implement exponential backoff
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000); // Max 30 seconds
      
      console.log(`Recovery attempt ${attempt}/${maxRetries} for ${error.type} after ${delay}ms`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      
      try {
        // The actual retry logic would be implemented by the calling service
        // This is just the framework for retry timing
        return true;
      } catch (retryError) {
        console.error(`Recovery attempt ${attempt} failed:`, retryError);
        
        if (attempt === maxRetries) {
          console.error(`All recovery attempts failed for ${error.type}`);
          return false;
        }
      }
    }

    return false;
  }

  /**
   * Log error for monitoring and debugging
   */
  logError(error: PlatyfendError): void {
    const logData = {
      type: error.type,
      message: error.message,
      severity: error.severity,
      context: error.context,
      retryable: error.retryable,
      timestamp: new Date().toISOString()
    };

    // Log to console (in production, this would go to a proper logging service)
    if (error.severity === 'critical' || error.severity === 'high') {
      console.error('Platyfend Error:', logData);
    } else {
      console.warn('Platyfend Warning:', logData);
    }

    // In production, send to monitoring service (e.g., Sentry, DataDog)
    // await this.sendToMonitoring(logData);
  }
}

// Singleton instance
export const errorHandlingService = new ErrorHandlingService();

// Helper functions
export function createErrorContext(
  userId?: string,
  organizationId?: string,
  installationId?: string,
  repositoryId?: string,
  operation?: string
): ErrorContext {
  return {
    userId,
    organizationId,
    installationId,
    repositoryId,
    operation,
    timestamp: new Date()
  };
}

export function isRetryableError(error: any): boolean {
  if (error.response?.status >= 500) return true;
  if (error.response?.status === 429) return true;
  if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') return true;
  return false;
}
