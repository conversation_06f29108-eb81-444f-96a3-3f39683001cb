/**
 * Legacy Webhook Sync Service
 * @deprecated Use the new webhook orchestrator and specialized handlers instead
 * This file is kept for backward compatibility
 */

import { WebhookSyncResult } from '@/src/lib/types/webhooks';
import {
  handleInstallationWebhook as handleInstallation,
  handleInstallationRepositoriesWebhook as handleInstallationRepositories,
  handlePullRequestWebhook as handlePullRequest,
  handleRepositoryWebhook as handleRepository,
} from './webhook/orchestrator';

/**
 * @deprecated Legacy WebhookSyncService class
 * Use the new webhook orchestrator and specialized handlers instead
 */
export class WebhookSyncService {
  /**
   * @deprecated Use pullRequestWebhookHandler.handlePullRequestEvent instead
   */
  async handlePullRequestEvent(payload: unknown, headers?: { event: string; delivery: string }): Promise<WebhookSyncResult> {
    console.warn('⚠️ WebhookSyncService.handlePullRequestEvent is deprecated. Use pullRequestWebhookHandler.handlePullRequestEvent instead.');
    const webhookHeaders = headers ? {
      'x-github-event': headers.event,
      'x-github-delivery': headers.delivery,
    } : undefined;
    return handlePullRequest(payload, webhookHeaders);
  }

  /**
   * @deprecated Use installationWebhookHandler.handleInstallationEvent instead
   */
  async handleInstallationEvent(payload: unknown): Promise<WebhookSyncResult> {
    console.warn('⚠️ WebhookSyncService.handleInstallationEvent is deprecated. Use installationWebhookHandler.handleInstallationEvent instead.');
    return handleInstallation(payload);
  }

  /**
   * @deprecated Use installationWebhookHandler.handleInstallationRepositoriesEvent instead
   */
  async handleInstallationRepositoriesEvent(payload: unknown): Promise<WebhookSyncResult> {
    console.warn('⚠️ WebhookSyncService.handleInstallationRepositoriesEvent is deprecated. Use installationWebhookHandler.handleInstallationRepositoriesEvent instead.');
    return handleInstallationRepositories(payload);
  }

  /**
   * @deprecated Use repositoryWebhookHandler.handleRepositoryEvent instead
   */
  async handleRepositoryEvent(payload: unknown): Promise<WebhookSyncResult> {
    console.warn('⚠️ WebhookSyncService.handleRepositoryEvent is deprecated. Use repositoryWebhookHandler.handleRepositoryEvent instead.');
    return handleRepository(payload);
  }
}

// Singleton instance for backward compatibility
export const webhookSyncService = new WebhookSyncService();

// Helper functions for webhook handlers (backward compatibility)
export async function handleInstallationWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return handleInstallation(payload);
}

export async function handleInstallationRepositoriesWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return handleInstallationRepositories(payload);
}

export async function handlePullRequestWebhook(
  payload: unknown,
  headers?: { event: string; delivery: string }
): Promise<WebhookSyncResult> {
  const webhookHeaders = headers ? {
    'x-github-event': headers.event,
    'x-github-delivery': headers.delivery,
  } : undefined;
  return handlePullRequest(payload, webhookHeaders);
}

export async function handleRepositoryWebhook(payload: unknown): Promise<WebhookSyncResult> {
  return handleRepository(payload);
}