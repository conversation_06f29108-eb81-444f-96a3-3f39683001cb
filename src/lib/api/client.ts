/**
 * Unified API Client
 * Standardized HTTP client with consistent error handling, retries, and logging
 */

import { APIResponse, PlatyfendError, RequestContext } from '@/src/lib/types/api';

/**
 * HTTP methods
 */
export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * Request configuration
 */
export interface RequestConfig {
  method?: HTTPMethod;
  headers?: Record<string, string>;
  body?: unknown;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  signal?: AbortSignal;
}

/**
 * API client configuration
 */
export interface APIClientConfig {
  baseURL?: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  defaultHeaders?: Record<string, string>;
  onRequest?: (config: RequestConfig) => void | Promise<void>;
  onResponse?: (response: Response) => void | Promise<void>;
  onError?: (error: PlatyfendError, context: RequestContext) => void | Promise<void>;
}

/**
 * Response wrapper
 */
export interface APIClientResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
  url: string;
}

/**
 * Unified API Client
 */
export class APIClient {
  private baseURL: string;
  private timeout: number;
  private retries: number;
  private retryDelay: number;
  private defaultHeaders: Record<string, string>;
  private onRequest?: (config: RequestConfig) => void | Promise<void>;
  private onResponse?: (response: Response) => void | Promise<void>;
  private onError?: (error: PlatyfendError, context: RequestContext) => void | Promise<void>;

  constructor(config: APIClientConfig = {}) {
    this.baseURL = config.baseURL || '';
    this.timeout = config.timeout || 30000;
    this.retries = config.retries || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...config.defaultHeaders,
    };
    this.onRequest = config.onRequest;
    this.onResponse = config.onResponse;
    this.onError = config.onError;
  }

  /**
   * Make HTTP request
   */
  async request<T = unknown>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<APIClientResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.timeout,
      retries = this.retries,
      retryDelay = this.retryDelay,
      signal,
    } = config;

    const url = this.buildURL(endpoint);
    const requestHeaders = { ...this.defaultHeaders, ...headers };
    
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    // Use provided signal or timeout signal
    const requestSignal = signal || controller.signal;

    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
      signal: requestSignal,
    };

    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    // Call request hook
    if (this.onRequest) {
      await this.onRequest(config);
    }

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, requestConfig);
        clearTimeout(timeoutId);

        // Call response hook
        if (this.onResponse) {
          await this.onResponse(response);
        }

        // Handle HTTP errors
        if (!response.ok) {
          const errorData = await this.parseErrorResponse(response);
          throw new APIError(
            `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            errorData,
            url
          );
        }

        // Parse response
        const data = await this.parseResponse<T>(response);
        
        return {
          data,
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          url,
        };
      } catch (error) {
        clearTimeout(timeoutId);
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Don't retry on certain errors
        if (
          error instanceof APIError && 
          (error.status < 500 || error.status === 501 || error.status === 505)
        ) {
          break;
        }

        // Don't retry on abort
        if (lastError.name === 'AbortError') {
          break;
        }

        // Wait before retry (except on last attempt)
        if (attempt < retries) {
          const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Handle final error
    const context: RequestContext = {
      operation: `${method} ${endpoint}`,
      timestamp: new Date(),
    };

    const platyfendError = this.createPlatyfendError(lastError!, context);
    
    if (this.onError) {
      await this.onError(platyfendError, context);
    }

    throw platyfendError;
  }

  /**
   * GET request
   */
  async get<T = unknown>(endpoint: string, config?: Omit<RequestConfig, 'method'>): Promise<APIClientResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T = unknown>(endpoint: string, body?: unknown, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<APIClientResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body });
  }

  /**
   * PUT request
   */
  async put<T = unknown>(endpoint: string, body?: unknown, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<APIClientResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body });
  }

  /**
   * PATCH request
   */
  async patch<T = unknown>(endpoint: string, body?: unknown, config?: Omit<RequestConfig, 'method' | 'body'>): Promise<APIClientResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body });
  }

  /**
   * DELETE request
   */
  async delete<T = unknown>(endpoint: string, config?: Omit<RequestConfig, 'method'>): Promise<APIClientResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  /**
   * Build full URL
   */
  private buildURL(endpoint: string): string {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    
    const base = this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL;
    const path = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    
    return `${base}${path}`;
  }

  /**
   * Parse response data
   */
  private async parseResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      return response.json();
    }
    
    if (contentType?.includes('text/')) {
      return response.text() as unknown as T;
    }
    
    return response.blob() as unknown as T;
  }

  /**
   * Parse error response
   */
  private async parseErrorResponse(response: Response): Promise<unknown> {
    try {
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        return await response.json();
      }
      return await response.text();
    } catch {
      return null;
    }
  }

  /**
   * Create Platyfend error from generic error
   */
  private createPlatyfendError(error: Error, context: RequestContext): PlatyfendError {
    if (error instanceof APIError) {
      return {
        type: 'external_api_error',
        message: error.message,
        provider: 'internal',
        statusCode: error.status,
        response: error.data,
      };
    }

    if (error.name === 'AbortError') {
      return {
        type: 'external_api_error',
        message: 'Request timeout',
        provider: 'internal',
        statusCode: 408,
      };
    }

    return {
      type: 'external_api_error',
      message: error.message || 'Unknown API error',
      provider: 'internal',
    };
  }
}

/**
 * API Error class
 */
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public data?: unknown,
    public url?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

/**
 * Default API client instance
 */
export const apiClient = new APIClient({
  onRequest: async (config) => {
    console.log(`🔄 API Request: ${config.method || 'GET'}`, config);
  },
  onResponse: async (response) => {
    console.log(`✅ API Response: ${response.status} ${response.statusText}`);
  },
  onError: async (error, context) => {
    console.error(`❌ API Error in ${context.operation}:`, error);
  },
});

/**
 * Create API client for external services
 */
export function createExternalAPIClient(baseURL: string, config?: Omit<APIClientConfig, 'baseURL'>): APIClient {
  return new APIClient({
    ...config,
    baseURL,
  });
}
